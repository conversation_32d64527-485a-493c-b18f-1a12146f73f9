<?php

namespace AppBundle\Command;

use AppBundle\Entity\Episode;
use AppBundle\Entity\Season;
use DateTime;
use DateTimeZone;
use Ivory\CKEditorBundle\Exception\Exception;
use MediaBundle\Entity\Media;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class SeasonResetOldDataCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:daily:seasonResetOldData');
    }

    /**
     * @throws \DateMalformedStringException
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $start = microtime(true);
            $em = $this->getContainer()->get('doctrine')->getManager();
            $posters = $em->getRepository("AppBundle:Poster")->getNotProcessedSeriesPostersOld();
            $logger = new Logger('custom_logger');
            $logFile = $this->getContainer()->getParameter('kernel.logs_dir') . '/custom2.log';
            $logger->pushHandler(new StreamHandler($logFile, Logger::INFO));
            $ids =[];
            foreach ($posters as $poster) {
                $ids[] = $poster->getId();
            }
            $logger->info('ids :: '.count($ids));
            $logger->info(json_encode($ids));
            foreach ($posters as $poster) {
                $poster->setSeriesStatus(null);
                $poster->setSyncStatus(0);
                $em->flush();
            }
            $end = microtime(true);
            $time = $end - $start;
            $output->writeln("Time ::" . $time);
        }
        catch (Exception $exception)
        {
            $output->writeln("error ::" . $exception->getMessage());
        }

    }
}
