<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class DailyFirstMovieCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:daily:firstmovie')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $posters = $em->getRepository("AppBundle:Poster")->findBy(['first' => true]);
        $current = new \DateTime('now', new \DateTimeZone('Asia/Jerusalem'));
        $current->setTime(0,0);
        $count = 0;
        foreach($posters as $poster){
            if($poster->getFirstDate() && $poster->getFirstDate() < $current){
                $poster->setFirst(false);
                $poster->setFirstDate(null);
            }elseif(!$poster->getFirstDate()){
                $poster->setFirst(false);
            }
            $em->persist($poster);
            $em->flush();
            $count++;
        }
        $output->writeln("Remove {$count} movies from first page");
    }

}
