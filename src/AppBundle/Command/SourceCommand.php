<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class SourceCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:minute:source')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $connection = $this->getContainer()->get('doctrine')->getConnection();
        // $sql = "SELECT count(DISTINCT id) FROM `source_table` WHERE (poster_id is not null or episode_id is not null) and url is not null";
        // $statement = $connection->prepare($sql);
        // $statement->execute();
        // $totalRecord = $statement->fetchColumn();
        // $sql = "SELECT count(DISTINCT id) FROM `source_table` WHERE (poster_id is not null or episode_id is not null) and url is not null and is_link_checked = 1";
        // $statement = $connection->prepare($sql);
        // $statement->execute();
        // $totalProcessed = $statement->fetchColumn();
        // if($totalRecord == $totalProcessed){
        //     $sql = "UPDATE source_table set is_link_checked = 0";
        //     $statement = $connection->prepare($sql);
        //     $statement->execute();
        // }
        $sql = $sql = "SELECT id, url FROM `source_table` WHERE (poster_id is not null or episode_id is not null) and url is not null and (is_link_checked = 0 or is_link_checked is null) order by id DESC limit 100";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $results = $statement->fetchAll();
        $count = 0;
        foreach($results as $result){
            $url = $result['url'];
            $url = $this->encodeBrowserUrl($url);
            $id = $result['id'];
            $isInvalid = $this->checkUrl($url);
            $sql = "UPDATE source_table set is_link_checked = 1, is_link_invalid = {$isInvalid} where id =".$id;
            $statement = $connection->prepare($sql);
            $statement->execute();
            $count++;
        }
        $output->writeln("Checked {$count} source urls");
    }
    public function encodeBrowserUrl($url) {
        return str_replace(' ', '%20',$url);
    }

    public function checkUrl($url){
        $url = $url . '?token=sourcecheckcodeforadmin';
        // Initialize cURL session
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        // Execute cURL request
        $response = curl_exec($ch);
        if ($response === false) {
            return 1;
        } else {
            // Get HTTP response code
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            // Check response code
            if ($httpCode == 200) {
                return 0;
            }
            return 1;
        }
    }

}
