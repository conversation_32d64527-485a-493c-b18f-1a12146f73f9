<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Swift_Message;

class EmailCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:minute:emails')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $connection = $this->getContainer()->get('doctrine')->getConnection();
        $sql = "SELECT * FROM emails_table WHERE is_sent = 0 order by id asc limit 1";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $result = $statement->fetch();
        $count = 0;
        $errorMessage = "";
        $isSuccess = 1;
        if($result){
            $count = $result['email_count'];
            $subject = $result['subject'];
            $body = $result['body'];
            $id = $result['id'];
            $sql = "update emails_table set is_sent = 1 where id = " . $id;
            $statement = $connection->prepare($sql);
            $statement->execute();
            $emails = $result['emails'];
            $emails = explode(',', $emails);
            $emails = array_unique($emails);
            $chunks = array_chunk($emails, 10);
            foreach($chunks as $key => $emailAdresses){
                $mailerId = ($key + 1);
                if($mailerId == 1 && $id <= 1636){
                    $errorMessage .= "Chunk  $mailerId not sent =====";
                    continue;
                }
                $mailer = $this->getContainer()->get('swiftmailer.mailer.mailer' . $mailerId);
                $fromAddress = $mailerId == 2 ? 'add' . $mailerId . '@nachosisrael.com' : 'app' . $mailerId . '@nachosisrael.com';
                try{
                    foreach($emailAdresses as $to){
                        $message = (new Swift_Message($subject))
                            ->setFrom([ $fromAddress => 'Nachos'])
                            ->setTo($to)
                            ->setBody(
                                $body,
                                'text/html'
                            );
                        $data = $mailer->send($message);
                        sleep(1);
                    }
                    $errorMessage .= "Chunk  $mailerId sent =====";
                }catch(\Throwable $e){
                    $errorMessage .= "Error in chunk $mailerId =======".$e->getMessage();
                    $isSuccess = 0;
                }
            }

            $id = $result['id'];
            $sql = "update emails_table set is_success = {$isSuccess}, error_message ='{$errorMessage}' where id = " . $id;
            $statement = $connection->prepare($sql);
            $statement->execute();
        }
        $output->writeln("Send {$count} emails");
    }

}
