<?php

namespace AppBundle\Command;

use Guz<PERSON><PERSON>ttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class ResetSourceUrlFlagCommand extends ContainerAwareCommand
{

    private $customLogger;

    protected function initialize(InputInterface $input, OutputInterface $output)
    {
        // Create a logger that writes to var/logs/source_checking.log
        $logFile = $this->getContainer()->get('kernel')->getLogDir() . '/source_reset.log';
        $this->customLogger = new Logger('source_reset_checking');
        $this->customLogger->pushHandler(new StreamHandler($logFile, Logger::DEBUG));
    }

    private $httpClient;

    protected function configure()
    {
        $this
            ->setName('app:source:reset-source-url-flag');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->customLogger->info("cron for checking start");
        $em = $this->getContainer()->get('doctrine')->getManager();
        $objSetting = $em->getRepository("AppBundle:Settings")->findOneBy(array(), array());
        if ($objSetting) {
            if ($objSetting->getSourceLinkCheckStatus() == 1) {
                try {
                    $objSetting->setSourceLinkCheckStatus(2);
                    $em->persist($objSetting);
                    $em->flush();
                    $lastId = 0;
                    $batchSize = 5;

                    do {
                        $qb = $em->createQueryBuilder();
                        $sources = $qb->select('s')
                            ->from('AppBundle:Source', 's')
                            ->where('s.linkCheckStatus = :completed')
                            ->andWhere('s.id > :lastId') // assume Source has ID
                            ->andWhere($qb->expr()->orX(
                                's.episode IS NOT NULL',
                                's.poster IS NOT NULL'
                            ))
                            ->setParameter('completed', 2)
                            ->setParameter('lastId', $lastId)
                            ->orderBy('s.id', 'ASC')
                            ->setMaxResults($batchSize)
                            ->getQuery()
                            ->getResult();

                        foreach ($sources as $source) {
                            $source->setLinkCheckStatus(0);
                        }

                        $em->flush();
                        $em->clear();

                        $count = count($sources);
                        if ($count > 0) {
                            $lastId = end($sources)->getId();
                        }

                        $this->customLogger->info("Processed up to ID $lastId");
                    } while ($count === $batchSize);

                    $objSetting = $em->getRepository("AppBundle:Settings")->findOneBy(array(), array());
                    if ($objSetting) {
                        $objSetting->setSourceLinkCheckStatus(0);
                        $em->persist($objSetting);
                    }
                } catch (\Exception $e) {
                    $this->customLogger->error("Error occurred during batch update: " . $e->getMessage());
                }
            }
        }
        $em->flush();
        $this->customLogger->info("cron for checking end");
    }
}