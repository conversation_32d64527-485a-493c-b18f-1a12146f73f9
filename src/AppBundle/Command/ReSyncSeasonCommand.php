<?php

namespace AppBundle\Command;

use AppBundle\Entity\Episode;
use AppBundle\Entity\Season;
use DateTime;
use Exception;
use MediaBundle\Entity\Media;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use GuzzleHttp\Exception\ClientException;

class ReSyncSeasonCommand extends ContainerAwareCommand
{
    private $client;

    protected function configure()
    {
        $this
            ->setName('ReSyncSeason')
            ->setDescription('Resyncs a season by Poster ID')
            ->addArgument('posterId', InputArgument::REQUIRED, 'The ID of the Poster to sync');
    }


    /**
     * @throws \DateMalformedStringException
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $posterId = $input->getArgument('posterId');

        $apiKeys = $this->getContainer()->getParameter('api_key_tmdb');
        $start = microtime(true);
        $logDir = $this->getContainer()->get('kernel')->getLogDir() . '/ReSyncSeason';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        $logFile = $logDir . '/' . date('Y-m-d') . '.log';

        $logger = new Logger('tmdb');
        $logger->pushHandler(new StreamHandler($logFile, Logger::INFO));

        $uniqid = uniqid();
        $logger->info($uniqid . ":: data sync started");
        $this->client = $this->getContainer()->get('app.guzzle_client');
        $apiKeys = $this->getContainer()->getParameter('api_key_tmdb');
        $em = $this->getContainer()->get('doctrine')->getManager();
        $posters = $em->getRepository("AppBundle:Poster")->findBy([
            'type' => 'serie',
            'id' => $posterId
        ]);

        foreach ($posters as $poster) {
            $poster->setSyncStatus(1);
            try {
                $id = $poster->getIdmid();
                $output->writeln("starting syncing for ID::" . $poster->getId() . " Title ::" . $poster->getTitle());
                $logger->info($uniqid . "::" . "starting syncing for ID::" . $poster->getId() . " Title ::" . $poster->getTitle() . " Idmid ::" . $poster->getIdmid());
                $responseBody = $this->client->request('GET', "/3/find/$id", [
                    'query' => [
                        'api_key' => $apiKeys,
                        'language' => 'he-IL',
                        'external_source' => 'imdb_id'
                    ]
                ]);
                $detailResponse = $responseBody->getBody();
                $detailResponse = json_decode($detailResponse->getContents(), true);
                if (empty($detailResponse['tv_results'][0]['id'])) {
                    $output->writeln("Serie not found");
                    $logger->info($uniqid . ":: Serie not found");
                    $poster->setSyncStatus(2);
                    $em->persist($poster);
                    $em->flush();
                    continue;
                }
                $mvoieDBSerieId = $detailResponse['tv_results'][0]['id'];
                if (!empty($detailResponse['tv_results'][0]['first_air_date'])) {
                    $poster->setFirstAirDate(new DateTime($detailResponse['tv_results'][0]['first_air_date']));
                }
                $responseBody = $this->client->request('GET', "/3/tv/$mvoieDBSerieId", [
                    'query' => [
                        'api_key' => $apiKeys,
                        'language' => 'he-IL',
                    ]
                ]);
                $detailResponse = $responseBody->getBody()->getContents();
                $response = json_decode($detailResponse, true);
                if (isset($response['success']) && $response['success'] === false) {
                    $output->writeln("status not success for ::" . $response['status_message']);
                    $logger->info($uniqid . "::" . "status not success for ::" . $response['status_message']);
                    $poster->setSyncStatus(2);
                    $em->persist($poster);
                    $em->flush();
                    continue;
                }
                $posterStatus = $response['status'];
                if (isset($response['next_episode_to_air']) && $response['next_episode_to_air'] != "") {
                    $nextEpisode = $response['next_episode_to_air'];
                    if (isset($nextEpisode['air_date']) && $nextEpisode['air_date'] != "") {
                        $poster->setNextAirDate(new DateTime($nextEpisode['air_date']));
                    }
                    if ($posterStatus == 'Returning Series') {
                        $posterStatus = 'Airing';
                    }
                }
                $poster->setSeriesStatus($posterStatus);
                $allSeasonAdded = false;
                $noOfSeason = $response['number_of_seasons'];
                $moviedbSeasons = $response['seasons'];
                if (count($moviedbSeasons) > 0) {
                    if ($moviedbSeasons[0]['season_number'] == 0) {
                        array_splice($moviedbSeasons, 0, 1);
                    }
                }
                $this->addMissingSeason($poster, $noOfSeason, $moviedbSeasons);
                $em->refresh($poster);
                $logger->info($uniqid . "::" . "missing season added ");
                $seasons = $poster->getSeasons();
                foreach ($seasons as $key => $season) {
                    $season_no = $key + 1;
                    if ($season_no <= $noOfSeason) {
                        $responseBody = $this->client->request('GET', "/3/tv/$mvoieDBSerieId/season/{$season_no}", [
                            'query' => [
                                'api_key' => $apiKeys,
                                'language' => 'he-IL',
                            ]
                        ]);
                        $detailResponse = $responseBody->getBody()->getContents();
                        $response = json_decode($detailResponse, true);
                        if (isset($response['episodes'])) {
                            $logger->info($uniqid . "::" . "episodes get");
                            $currentEpisodes = $season->getEpisodes();
                            $moviedbEpisodes = $response['episodes'];
                            foreach ($moviedbEpisodes as $responseEpisodeKey => $responseEpisode) {
                                if ($responseEpisode['air_date'] < date("Y-m-d")) {
                                    $tempVal = $responseEpisodeKey + 1;
                                    if (isset($currentEpisodes[$responseEpisodeKey])) {
                                        $objEpisode = $currentEpisodes[$responseEpisodeKey];
                                        $objEpisode->setAirDate(new \DateTime($responseEpisode['air_date']));
                                    } else {
                                        $objEpisode = new Episode();
                                        $objEpisode->setSeason($season);
                                        $objEpisode->setTitle('פרק ' . $tempVal);
                                        $objEpisode->setDescription($responseEpisode['overview']);
                                        $objEpisode->setAirDate(new \DateTime($responseEpisode['air_date']));
                                        $objEpisode->setPlayas(2);
                                        $objEpisode->setEnabled(1);
                                        $objEpisode->setPosition($tempVal);

                                    }
                                    if ($responseEpisode["still_path"] && !$objEpisode->getMedia()) {
                                        $url = 'https://tmdb-image-prod.b-cdn.net/t/p/w1920_and_h800_multi_faces' . $responseEpisode["still_path"];
                                        $fileName = md5(uniqid());
                                        list($fileType, $fileExt) = $this->get_image_mime_type($url);
                                        $fullName = $fileName . "." . $fileExt;
                                        $uploadTo = $this->getContainer()->getParameter('files_directory') . $fileExt . "/" . $fullName;
                                        $mediaContent = file_get_contents($url);
                                        if ($mediaContent) {
                                            file_put_contents($uploadTo, $mediaContent);
                                        }
                                        $moviemedia = new Media();
                                        $moviemedia->setType($fileType);
                                        $moviemedia->setExtension($fileExt);
                                        $moviemedia->setUrl($fullName);
                                        $moviemedia->setTitre($objEpisode->getTitle());
                                        $em->persist($moviemedia);
                                        $objEpisode->setMedia($moviemedia);
                                    }
                                    $em->persist($objEpisode);
                                }
                            }
                            $em->flush();
                            $allEpisodeAdded = false;
                            $currentEpisodesCnt = $em->getRepository('AppBundle:Episode')->countBySeries($season);
                            if ($moviedbSeasons[$key]['episode_count'] == $currentEpisodesCnt) {
                                $allEpisodeAdded = true;
                            }
                            $seasonAirDate = $moviedbSeasons[$key]['air_date'];
                            if ($seasonAirDate) {
                                $seasonAirDate = new DateTime($moviedbSeasons[$key]['air_date']);
                            }
                            $season->setAllEpisodeAdded($allEpisodeAdded);
                            $season->setAirDate($seasonAirDate);
                            $em->persist($season);
                        }
                    } else {
                        $logger->info($uniqid . "::" . "extra season in " . $poster->getId() . " season number " . $season_no);
                    }
                }
                if (count($poster->getSeasons()) >= $noOfSeason) {
                    $allSeasonAdded = true;
                }
                $poster->setAllSeasonAdded($allSeasonAdded);
                $poster->setIdmTotalSeason($noOfSeason);
                $poster->setSyncStatus(2);
                $em->persist($poster);
                $em->flush();
                $output->writeln("end syncing for ID::" . $poster->getId());
                $logger->info($uniqid . "::" . "end syncing for ID::" . $poster->getId());
            } catch (ClientException $e) {
                $syncStatus = 0;
                $em->refresh($poster);
                if ($e->getResponse()->getStatusCode() === 404) {
                    $syncStatus = 2;
                    $logger->info($uniqid . "::" . "404 syncing for ID::" . $poster->getId());
                }
                $poster->setSyncStatus($syncStatus);
                $em->persist($poster);
                $em->flush();
                $logger->info($uniqid . "::" . "error in syncing for ID::" . $e->getMessage());
            } catch (Exception $e) {
                $em->refresh($poster);
                $poster->setSyncStatus(0);
                $em->persist($poster);
                $em->flush();
                $output->writeln("error in syncing for ID::" . $e->getMessage());
                $logger->info($uniqid . "::" . "error in syncing for ID::" . $e->getMessage());
            }
        }
        $end = microtime(true);
        $time = $end - $start;
        $output->writeln("Time ::" . $time);
        $logger->info($uniqid . "::" . "Time ::" . $time);
    }


    public function addMissingSeason($serie, $total_season, $moviedbSeasons)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $max = 0;
        $seasons = $serie->getSeasons();
        $currentSeasonCount = 0;
        foreach ($seasons as $key => $value) {
            if ($value->getPosition() > $max) {
                $max = $value->getPosition();
            }
            $currentSeasonCount++;
        }
        $total_season = $total_season - $currentSeasonCount;
        if ($total_season > 0) {
            $moviedbSeasons = array_slice($moviedbSeasons, $total_season * -1);
            for ($i = 0; $i < $total_season; $i++) {
                $max++;
                if (isset($moviedbSeasons[$i]['air_date']) && $moviedbSeasons[$i]['air_date'] != null) {
                    if ($moviedbSeasons[$i]['air_date'] < date("Y-m-d")) {
                        $season_new = new Season();
                        $season_new->setTitle($max . ' עונה');
                        $season_new->setPoster($serie);
                        $season_new->setPosition($max);
                        $season_new->setAirDate(new DateTime($moviedbSeasons[$i]['air_date']));
                        $em->persist($season_new);
                    }
                }
            }
        }
        $em->flush();
    }

    public function addMissingEpisode($season, $total_episode, $user)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $max = 0;
        $currentEpisodeCount = 0;
        $episodes = $season->getEpisodes();
        foreach ($episodes as $key => $value) {
            if ($value->getPosition() > $max) {
                $max = $value->getPosition();
            }
            $currentEpisodeCount++;
        }
        $total_episode = $total_episode - $currentEpisodeCount;
        if ($total_episode > 0) {
            for ($i = 0; $i < $total_episode; $i++) {
                $max++;
                $episode_new = new Episode();
                $episode_new->setSeason($season);
                $episode_new->setTitle('פרק ' . $max);
                $episode_new->setDescription('');
                $episode_new->setPlayas(2);
                $episode_new->setEnabled(1);
                $episode_new->setPosition($max);
                $episode_new->setUser($user);
                $em->persist($episode_new);
            }
        }
        $em->flush();
    }


    private function curlGetRequest($url)
    {
        $timeOut = 10;
        // Initialize a cURL session
        $ch = curl_init();

        // Set the URL to fetch
        curl_setopt($ch, CURLOPT_URL, $url);

        // Return the transfer as a string
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeOut);

        // Execute the request and store the response
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            return false;
        } else {
            // Output the response
            return json_decode($response, true);
        }

        // Close the cURL session
        curl_close($ch);
    }

    function get_image_mime_type($image_path)
    {
        $ext_mimes = array(
            IMAGETYPE_GIF => "gif",
            IMAGETYPE_JPEG => "jpg",
            IMAGETYPE_PNG => "png",
            IMAGETYPE_SWF => "swf",
            IMAGETYPE_PSD => "psd",
            IMAGETYPE_BMP => "bmp",
            IMAGETYPE_TIFF_II => "tiff",
            IMAGETYPE_TIFF_MM => "tiff",
            IMAGETYPE_JPC => "jpc",
            IMAGETYPE_JP2 => "jp2",
            IMAGETYPE_JPX => "jpx",
            IMAGETYPE_JB2 => "jb2",
            IMAGETYPE_SWC => "swc",
            IMAGETYPE_IFF => "iff",
            IMAGETYPE_WBMP => "wbmp",
            IMAGETYPE_XBM => "xbm",
            IMAGETYPE_ICO => "ico");

        $mimes = array(
            IMAGETYPE_GIF => "image/gif",
            IMAGETYPE_JPEG => "image/jpg",
            IMAGETYPE_PNG => "image/png",
            IMAGETYPE_SWF => "image/swf",
            IMAGETYPE_PSD => "image/psd",
            IMAGETYPE_BMP => "image/bmp",
            IMAGETYPE_TIFF_II => "image/tiff",
            IMAGETYPE_TIFF_MM => "image/tiff",
            IMAGETYPE_JPC => "image/jpc",
            IMAGETYPE_JP2 => "image/jp2",
            IMAGETYPE_JPX => "image/jpx",
            IMAGETYPE_JB2 => "image/jb2",
            IMAGETYPE_SWC => "image/swc",
            IMAGETYPE_IFF => "image/iff",
            IMAGETYPE_WBMP => "image/wbmp",
            IMAGETYPE_XBM => "image/xbm",
            IMAGETYPE_ICO => "image/ico");

        if (($image_type = exif_imagetype($image_path))
            && (array_key_exists($image_type, $mimes))) {
            return [$mimes[$image_type], $ext_mimes[$image_type]];
        } else {
            return FALSE;
        }
    }

    function get_image_ext_type($image_path)
    {
        $mimes = array(
            IMAGETYPE_GIF => "gif",
            IMAGETYPE_JPEG => "jpg",
            IMAGETYPE_PNG => "png",
            IMAGETYPE_SWF => "swf",
            IMAGETYPE_PSD => "psd",
            IMAGETYPE_BMP => "bmp",
            IMAGETYPE_TIFF_II => "tiff",
            IMAGETYPE_TIFF_MM => "tiff",
            IMAGETYPE_JPC => "jpc",
            IMAGETYPE_JP2 => "jp2",
            IMAGETYPE_JPX => "jpx",
            IMAGETYPE_JB2 => "jb2",
            IMAGETYPE_SWC => "swc",
            IMAGETYPE_IFF => "iff",
            IMAGETYPE_WBMP => "wbmp",
            IMAGETYPE_XBM => "xbm",
            IMAGETYPE_ICO => "ico");

        if (($image_type = exif_imagetype($image_path))
            && (array_key_exists($image_type, $mimes))) {
            return $mimes[$image_type];
        } else {
            return FALSE;
        }
    }
}
