<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class SeasonCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:daily:season')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $connection = $this->getContainer()->get('doctrine')->getConnection();
        $sql = "SELECT count(DISTINCT id) FROM `poster_table` WHERE `type` = 'serie' and idmid is not null";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $totalRecord = $statement->fetchColumn();
        $sql = "SELECT count(DISTINCT id) FROM `poster_table` WHERE `type` = 'serie' and idmid is not null and is_processed = 1";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $totalProcessed = $statement->fetchColumn();
        if($totalRecord == $totalProcessed){
            $sql = "UPDATE poster_table set is_processed = 0";
            $statement = $connection->prepare($sql);
            $statement->execute();
        }
        $sql = "SELECT p.id as poster_id, p.idmid, count(DISTINCT s.id) as totalSeason FROM `poster_table` p 
                JOIN season_table s on s.poster_id = p.id
                WHERE p.type = 'serie' and p.idmid is not null and p.is_processed = 0 group by p.id order by p.id ASC limit 20";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $results = $statement->fetchAll();
        $count = 0;
        foreach($results as $result){
            $seasonCount =  $this->getSeasonCount($result['idmid']);
            if($seasonCount !== false && $seasonCount != 'N/A'){
                $addedAll = 0;
                if($result['totalSeason'] == $seasonCount){
                    $addedAll = 1;
                }
                $sql = "UPDATE poster_table set is_processed = 1, idm_total_season = {$seasonCount}, all_season_added={$addedAll} where id = {$result['poster_id']}";
                $statement = $connection->prepare($sql);
                $statement->execute();
            }else{
                $sql = "UPDATE poster_table set is_processed = 1 where id = {$result['poster_id']}";
                $statement = $connection->prepare($sql);
                $statement->execute();
            }
            $count++;
        }
        $output->writeln("Checked {$count} series season");
    }

    public function getSeasonCount($idmid){
        $apiKey = 'f772931b'; // Replace 'YOUR_API_KEY' with your actual API key
        
        // URL for the OMDB API
        $url = "https://www.omdbapi.com/?i=$idmid&apikey=$apiKey";
        
        // Initialize cURL session
        $ch = curl_init();
        
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        // Execute cURL session
        $response = curl_exec($ch);
        
        // Check for errors
        if ($response === false) {
            return false;
        } else {
            // Decode the JSON response
            $data = json_decode($response, true);
            return isset($data['totalSeasons']) ? $data['totalSeasons'] : false ;
        }
        
        // Close cURL session
        curl_close($ch);
    }

}
