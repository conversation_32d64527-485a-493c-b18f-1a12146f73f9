<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use AppBundle\Entity\Season;
use AppBundle\Entity\Episode;
use MediaBundle\Entity\Media;
use AppBundle\Entity\SerieApiCall;

class SerieApiCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:minute:serie-api');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $serieApiCall = $em->getRepository("AppBundle:SerieApiCall")->findOneBy(['processed' => false, 'progress' => false]);
        $count = 0;
        try{
            if($serieApiCall){
                $count = 1;
                $serieApiCall->setProgress(true);
                $user = $serieApiCall->getUser();
                $em->persist($serieApiCall);
                $em->flush();
                $em->refresh($serieApiCall);
                $id = $serieApiCall->getSerieId();
                $serie=$em->getRepository("AppBundle:Poster")->findOneBy(array("id"=>$id,"type"=>"serie"));
                $id = $serie->getIdmid();
                $url = "https://api.themoviedb.org/3/find/{$id}?api_key=10471161c6c1b74f6278ff73bfe95982&language=he-IL&external_source=imdb_id";
                $response = $this->curlGetRequest($url);
                //var_dump($response['tv_results'][0]['id']);exit;
                if(empty($response['tv_results'][0]['id'])){
                    throw new \Exception('Serie not found');
                }
                $mvoieDBSerieId = $response['tv_results'][0]['id'];
                $url = "https://api.themoviedb.org/3/tv/{$mvoieDBSerieId}?api_key=10471161c6c1b74f6278ff73bfe95982&language=he-IL";
                $response = $this->curlGetRequest($url);
                if(isset($response['success']) && $response['success'] === false){
                    throw new \Exception($response['status_message']);
                }
                $noOfSeason = $response['number_of_seasons'];
                $moviedbSeasons = $response['seasons'];
                $this->addMissingSeason($serie, $noOfSeason, $user);
                $em->refresh($serie);
                $seasons = $serie->getSeasons();
                foreach($seasons as $key => $season){
                    if(isset($moviedbSeasons[$key])){
                        $this->addMissingEpisode($season, $moviedbSeasons[$key]['episode_count'], $user);
                    }
                }
                $em->refresh($serie);
                $seasons = $serie->getSeasons();
                foreach($seasons as $key => $season){
                    $em->refresh($season);
                    $season_no = $key + 1;
                    $url = "https://api.themoviedb.org/3/tv/{$mvoieDBSerieId}/season/{$season_no}?api_key=10471161c6c1b74f6278ff73bfe95982&language=he-IL";
                    $response = $this->curlGetRequest($url);
                    if(isset($response['episodes'])){
                        $moviedbEpisodes = $response['episodes'];
                        // $url = "https://api.themoviedb.org/3/tv/{$mvoieDBSerieId}/season/{$season_no}?api_key=10471161c6c1b74f6278ff73bfe95982";
                        // $response = $this->curlGetRequest($url);
                        // $moviedbEpisodesEN = $response['episodes'];
                        $episodes = $season->getEpisodes();
                        foreach($episodes as $ekey => $episode){
                            if(isset($moviedbEpisodes[$ekey])){
                                $moviedbEpisode = $moviedbEpisodes[$ekey];
                                //$moviedbEpisodeEN = $moviedbEpisodesEN[$ekey];
                                $overview = $moviedbEpisode['overview'];
                                // $overview = $moviedbEpisode['overview'] ? $moviedbEpisode['overview'] : $moviedbEpisodeEN['overview'];
                                $episode->setDescription($overview);
                                if($moviedbEpisode["still_path"] && !$episode->getMedia()){
                                    try{

                                        $url =  'https://tmdb-image-prod.b-cdn.net/t/p/w1920_and_h800_multi_faces'.$moviedbEpisode["still_path"];
                                        
                                        $ch = curl_init($url);
                                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); // Connection timeout in seconds
                                        curl_setopt($ch, CURLOPT_TIMEOUT, 3); // Total execution timeout in seconds

                                        $response = curl_exec($ch);

                                        if(curl_errno($ch)) {
                                            throw new \Exception( 'Request Error:' . curl_error($ch), 1);
                                        }
                                        curl_close($ch);

                                        $fileName = md5(uniqid());
                                        list($fileType, $fileExt) = $this->get_image_mime_type($url);
                                        $fullName = $fileName.".".$fileExt;
                
                                        $uploadTo = $this->getContainer()->getParameter('files_directory').$fileExt."/".$fullName;
                
                                        file_put_contents($uploadTo, file_get_contents($url)); 
                
                                        $moviemedia= new Media();
                                        $moviemedia->setType($fileType);
                                        $moviemedia->setExtension($fileExt);
                                        $moviemedia->setUrl($fullName);
                                        $moviemedia->setTitre($episode->getTitle());
                                        $em->persist($moviemedia);
                                        $episode->setMedia($moviemedia);
                                    }catch(\Throwable $e){
                                        $em->refresh($serieApiCall);
                                        $serieApiCall->addMessage($e->getMessage());
                                        $em->persist($serieApiCall);
                                        $em->flush($serieApiCall);
                                    }
                                }
                                $em->persist($episode);
                                $em->flush();
                            }
                        }
                    }
                }
                $em->refresh($serieApiCall);
                $serieApiCall->setProgress(false);
                $serieApiCall->setProcessed(true);
                $em->persist($serieApiCall);
                $em->flush($serieApiCall);
            }
        }catch(\Throwable $t){
            $em->refresh($serieApiCall);
            $serieApiCall->setProgress(false);
            $serieApiCall->setProcessed(true);
            $serieApiCall->setMessage($t->getMessage());
            $em->persist($serieApiCall);
            $em->flush($serieApiCall);
        }
        
        $output->writeln("process {$count} serie api");
    }

    public function addMissingSeason($serie, $total_season, $user){
        $em = $this->getContainer()->get('doctrine')->getManager();
        $max=0;
        $seasons=$em->getRepository('AppBundle:Season')->findBy(array("poster"=>$serie));
        $currentSeasonCount = 0;
        foreach ($seasons as $key => $value) {
            if ($value->getPosition()>$max) {
                $max=$value->getPosition();
            }
            $currentSeasonCount++;
        }
        $total_season = $total_season - $currentSeasonCount;
        if($total_season > 0){
            for($i = 0; $i < $total_season;$i++){
                $max++;
                $season_new = new Season();
                $season_new->setTitle($max . ' עונה');
                $season_new->setPoster($serie);
                $season_new->setPosition($max);
                $season_new->setUser($user);
                $em->persist($season_new);
                $em->flush();
            }
        }
    }

    public function addMissingEpisode($season, $total_episode, $user){
        $em = $this->getContainer()->get('doctrine')->getManager();
        $max=0;
        $currentEpisodeCount = 0;
        $episodes=$em->getRepository('AppBundle:Episode')->findBy(array("season"=>$season));
        foreach ($episodes as $key => $value) {
            if ($value->getPosition()>$max) {
                $max=$value->getPosition();
            }
            $currentEpisodeCount++;
        }
        $total_episode = $total_episode - $currentEpisodeCount;
        if($total_episode > 0){
            for($i = 0; $i < $total_episode;$i++){
                $max++;
                $episode_new = new Episode();
                $episode_new->setSeason($season);
                $episode_new->setTitle('פרק '.$max);
                $episode_new->setDescription('');
                $episode_new->setPlayas(2);
                $episode_new->setEnabled(1);
                $episode_new->setPosition($max);
                $episode_new->setUser($user);
                $em->persist($episode_new);
                $em->refresh($season);
                $em->flush();
            }
        }
    }

    private function curlGetRequest($url) {
        // Initialize a cURL session
        $ch = curl_init();
    
        // Set the URL to fetch
        curl_setopt($ch, CURLOPT_URL, $url);
        
        // Return the transfer as a string
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
        // Execute the request and store the response
        $response = curl_exec($ch);
    
        // Check for errors
        if (curl_errno($ch)) {
            return false;
        } else {
            // Output the response
            return json_decode($response, true);
        }
    
        // Close the cURL session
        curl_close($ch);
    }

    function get_image_mime_type($image_path)
    {
        $ext_mimes  = array(
            IMAGETYPE_GIF => "gif",
            IMAGETYPE_JPEG => "jpg",
            IMAGETYPE_PNG => "png",
            IMAGETYPE_SWF => "swf",
            IMAGETYPE_PSD => "psd",
            IMAGETYPE_BMP => "bmp",
            IMAGETYPE_TIFF_II => "tiff",
            IMAGETYPE_TIFF_MM => "tiff",
            IMAGETYPE_JPC => "jpc",
            IMAGETYPE_JP2 => "jp2",
            IMAGETYPE_JPX => "jpx",
            IMAGETYPE_JB2 => "jb2",
            IMAGETYPE_SWC => "swc",
            IMAGETYPE_IFF => "iff",
            IMAGETYPE_WBMP => "wbmp",
            IMAGETYPE_XBM => "xbm",
            IMAGETYPE_ICO => "ico");

        $mimes  = array(
            IMAGETYPE_GIF => "image/gif",
            IMAGETYPE_JPEG => "image/jpg",
            IMAGETYPE_PNG => "image/png",
            IMAGETYPE_SWF => "image/swf",
            IMAGETYPE_PSD => "image/psd",
            IMAGETYPE_BMP => "image/bmp",
            IMAGETYPE_TIFF_II => "image/tiff",
            IMAGETYPE_TIFF_MM => "image/tiff",
            IMAGETYPE_JPC => "image/jpc",
            IMAGETYPE_JP2 => "image/jp2",
            IMAGETYPE_JPX => "image/jpx",
            IMAGETYPE_JB2 => "image/jb2",
            IMAGETYPE_SWC => "image/swc",
            IMAGETYPE_IFF => "image/iff",
            IMAGETYPE_WBMP => "image/wbmp",
            IMAGETYPE_XBM => "image/xbm",
            IMAGETYPE_ICO => "image/ico");

        if (($image_type = exif_imagetype($image_path))
            && (array_key_exists($image_type ,$mimes)))
        {
            return [$mimes[$image_type], $ext_mimes[$image_type]];
        }
        else
        {
            return FALSE;
        }
    }
   function get_image_ext_type($image_path)
    {
        $mimes  = array(
            IMAGETYPE_GIF => "gif",
            IMAGETYPE_JPEG => "jpg",
            IMAGETYPE_PNG => "png",
            IMAGETYPE_SWF => "swf",
            IMAGETYPE_PSD => "psd",
            IMAGETYPE_BMP => "bmp",
            IMAGETYPE_TIFF_II => "tiff",
            IMAGETYPE_TIFF_MM => "tiff",
            IMAGETYPE_JPC => "jpc",
            IMAGETYPE_JP2 => "jp2",
            IMAGETYPE_JPX => "jpx",
            IMAGETYPE_JB2 => "jb2",
            IMAGETYPE_SWC => "swc",
            IMAGETYPE_IFF => "iff",
            IMAGETYPE_WBMP => "wbmp",
            IMAGETYPE_XBM => "xbm",
            IMAGETYPE_ICO => "ico");

        if (($image_type = exif_imagetype($image_path))
            && (array_key_exists($image_type ,$mimes)))
        {
            return $mimes[$image_type];
        }
        else
        {
            return FALSE;
        }
    }
}
