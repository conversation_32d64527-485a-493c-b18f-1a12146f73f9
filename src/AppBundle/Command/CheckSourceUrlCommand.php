<?php

namespace AppBundle\Command;

use Guz<PERSON><PERSON>ttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class CheckSourceUrlCommand extends ContainerAwareCommand
{

    private $customLogger;

    protected function initialize(InputInterface $input, OutputInterface $output)
    {
        // Create a logger that writes to var/logs/source_checking.log
        $logFile = $this->getContainer()->get('kernel')->getLogDir() . '/source_checking.log';
        $this->customLogger = new Logger('source_checking');
        $this->customLogger->pushHandler(new StreamHandler($logFile, Logger::DEBUG));
    }

    private $httpClient;

    protected function configure()
    {
        $this
            ->setName('app:source:check-urls');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $totalNoOfRecord = 250;
        $this->customLogger->info("cron for checking start");
        $this->httpClient = $this->getContainer()->get('app.http_client');
        $referer = $this->getContainer()->getParameter('base_url_frontend');
        $em = $this->getContainer()->get('doctrine')->getManager();
        $objSetting = $em->getRepository("AppBundle:Settings")->findOneBy(array(), array());
        if ($objSetting) {
            if ($objSetting->getSourceLinkCheckStatus() == 0) {
                $qb = $em->createQueryBuilder();
                $i = 0;
                $sources = $qb->select('s')
                    ->from('AppBundle:Source', 's')
                    ->where($qb->expr()->orX(
                        's.episode IS NOT NULL',
                        's.poster IS NOT NULL'
                    ))
                    ->andWhere('s.linkCheckStatus != :completed')
                    ->setParameter('completed', 2)
                    ->orderBy('s.id', 'DESC')
                    ->setMaxResults($totalNoOfRecord)
                    ->getQuery()
                    ->getResult();
                foreach ($sources as $source) {
                    $uniqueId = uniqid();
                    $videoUrl = $source->getUrl();
                    $this->customLogger->info($uniqueId . " start process ::" . $source->getId() . " url :: " . $videoUrl);
                    if ($videoUrl) {
                        try {
                            $response = $this->httpClient->request('GET', $videoUrl, [
                                'headers' => [
                                    'User-Agent' => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                                    'Referer' => $referer,
                                    'Accept' => '*/*',
                                    'Accept-Encoding' => 'identity;q=1, *;q=0',
                                    'Accept-Language' => 'en-US,en;q=0.9',
                                ],
                                'http_errors' => false,
                                'timeout' => 5,
                                'connect_timeout' => 5,
                                'stream' => true,
                            ]);
                            $statusCode = $response->getStatusCode();
                            $this->customLogger->info($uniqueId . " status code ::" . $statusCode);
                            if ($statusCode >= 200 && $statusCode < 400) {
                                $source->setLinkInvalid(0);
                            } else {
                                $source->setLinkInvalid(1);
                            }
                            $source->setLinkCheckStatus(2); // completed
                            $source->setLinkChecked(1);
                        } catch (ConnectException|RequestException $e) {
                            $this->customLogger->error($uniqueId . " exception ::" . $e->getMessage());
                            $source->setLinkInvalid(0);
                            $source->setLinkCheckStatus(2);
                            $source->setLinkChecked(1);
                        }
                    } else {
                        $source->setLinkInvalid(0);
                        $source->setLinkCheckStatus(2); // completed
                        $source->setLinkChecked(0);
                    }
                    if (++$i % 50 === 0) {
                        $em->flush();
                        sleep(1);
                    }
                }
                if (count($sources) < $totalNoOfRecord) {
                    $objSetting->setSourceLinkCheckStatus(1);
                    $em->persist($objSetting);
                }
            }
        }
        $em->flush();
        $this->customLogger->info("cron for checking end");
    }


}