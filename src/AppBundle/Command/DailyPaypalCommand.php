<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class DailyPaypalCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:daily:paypal')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $connection = $this->getContainer()->get('doctrine')->getConnection();
        $sql = "select uh.id, uh.email, uh.expired_at as expired_at, u.id as user_id from user_paypal_history uh join fos_user_table u on u.username = uh.email where uh.status = 'cancelled' and uh.expired_at is not null and uh.expired_at <= CURDATE()";
        $statement = $connection->prepare($sql);
        $statement->execute();

        // Fetch the results as an array
        $results = $statement->fetchAll();
        //var_dump($results);exit;
        $em = $this->getContainer()->get('doctrine')->getManager();
        $count = 0;
        foreach($results as $result){
            if($result['expired_at']){
                $userActiveCheck =  $em->getRepository("UserBundle:Paid")->findOneBy(array("email"=>$result['email']),array("id"=>"desc"));
                if($userActiveCheck && $userActiveCheck->getStatus() != 'active'){
                    $user = $em->getRepository("UserBundle:User")->find($result['user_id']);
                    $user->setPaymentStatus(0);
                    $em->persist($user);
                }
                $userPaid = $em->getRepository("UserBundle:Paid")->find($result['id']);
                $userPaid->setExpiredAt(null);
                $em->persist($userPaid);
                $em->flush();
                $count++;
            }else{
                $userPaid = $em->getRepository("UserBundle:Paid")->find($result['id']);
                $userPaid->setExpiredAt(null);
                $em->persist($userPaid);
                $em->flush();
            }
        }
        $output->writeln("Expired {$count} users plan");
    }

}
