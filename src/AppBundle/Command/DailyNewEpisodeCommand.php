<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

class DailyNewEpisodeCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:daily:newepisode')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $current = new \DateTime('now', new \DateTimeZone('Asia/Jerusalem'));
        $current->setTime(0,0);
        $count = 0;
        $queryBuilder = $em->getRepository('AppBundle:NewContent')->createQueryBuilder('n');
        $remindMeRepo = $em->getRepository('AppBundle:RemindMe');
        $queryBuilder->where('n.expireDate <= :currentDate')
                     ->setParameter('currentDate', $current->format('Y-m-d'));
        $newContents = $queryBuilder->getQuery()->getResult();
        foreach($newContents as $newContent){
            $poster = $newContent->getPoster();
            $em->remove($newContent);
            $em->flush();
            $remindMes = $remindMeRepo->findBy(['poster' => $poster]);
            foreach($remindMes as $remindMe){
                $remindMe->setAvailable(false);
                $em->persist($remindMe);
                $em->flush();
            }
            $count++;
        }
        $output->writeln("Remove {$count} series from new episode");
    }

}
