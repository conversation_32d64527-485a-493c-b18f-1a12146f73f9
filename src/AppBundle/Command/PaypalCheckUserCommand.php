<?php

namespace AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Swift_Message;

class PaypalCheckUserCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('sync:minute:check-paypal-users')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $connection = $this->getContainer()->get('doctrine')->getConnection();
        $sql = "SELECT id,email,paypal_information FROM user_paypal_history WHERE status = 'active' and paypal_id IS NOT NULL order by id asc";
        $statement = $connection->prepare($sql);
        $statement->execute();
        $results = $statement->fetchAll();
		$subscriptionIDs = [];
        foreach($results as $result){
            if($result['paypal_information'] == 'test'){
                continue;
            }
            $paypalInfo = json_decode($result['paypal_information'], true);
            $subscriptionID = $paypalInfo['subscriptionID'];
			if(isset($subscriptionIDs[$subscriptionID])){
				$subscriptionIDs[$subscriptionID] = $subscriptionIDs[$subscriptionID] + 1;
			}else{
				$subscriptionIDs[$subscriptionID] = 1;
			}
			//$subscriptionIDs[$subscriptionID] = $subscriptionID;
            // $data = $this->get_subs_details($subscriptionID);
            // if($data){
            //     if($data['status'] == 'ACTIVE'){
            //         $paymentStatus = 1;
            //     } else{
            //         $paymentStatus = -1;
            //     }
            // }else{
            //     $paymentStatus = 0;
            // }
            // $sql = "UPDATE user_paypal_history set is_processed = {$paymentStatus} where id = " . $result['id'];
            // $statement = $connection->prepare($sql);
            // $statement->execute();
        }
		$filteredIDs = array_filter($subscriptionIDs, function($value) {
			return $value >= 2;
		});
		var_dump($filteredIDs);exit;
		$subscriptionIDs = array_unique($subscriptionIDs);
		var_dump(count($subscriptionIDs));
        var_dump(count($results));exit;
    }

    private function get_subs_details($subs_id)
	{
		$access_token = $this->access_token();

		/* API URL */
		$url = 'https://api.paypal.com' . '/v1/billing/subscriptions/' . $subs_id; //. '/cancel';
		//$url = 'https://api-m.sandbox.paypal.com/v1/billing/subscriptions/' . $subs_id; //. '/cancel';

        $auth_name = 'AZz5rK4Nu-NCnGBTCnwWSOP-Fq5EZ-XEW3fjUBdfnqUCIldW_BZWMo913L_eJuNFdkYb48dxU-L47loX';
		$auth_pass = 'EED3laQzQtZgjF-3-eqzuE_xQP6Ql_w-pfh6bOLT8l39ETeF3FXfwew8qRco5aVyYyuCeFZXmtj9znBu';

		/* Init cURL resource */
		$ch = curl_init($url);

		/* Array Parameter Data */
		// $data = "grant_type=client_credentials"; // ['grant_type'=>'client_credentials'];

		/* pass encoded JSON string to the POST fields */
		// curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		// curl_setopt($ch, CURLOPT_NOBODY, true);  
		/* set the content type json */
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-Type:application/json',
			'Authorization: Bearer ' . $access_token,
		));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		// curl_setopt($ch, CURLOPT_USERPWD, $auth_name . ':' . $auth_pass);
		/* set return type json */
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		/* execute request */
		$result = curl_exec($ch);


		// $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
		// $header = substr($result, 0, $header_size);
		// $body = substr($result, $header_size);
		/* close cURL resource */
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);

		// echo $result;

		$response = json_decode($result, true);

		return $response;
		// echo json_encode($response);
		// echo json_encode($httpcode);
	}

    private function access_token()
	{
		/* API URL */
		$url = 'https://api.paypal.com' . '/v1/oauth2/token';
		//$url = 'https://api-m.sandbox.paypal.com/v1/oauth2/token';

		/* SEMDBOX */
		$auth_name = 'AZz5rK4Nu-NCnGBTCnwWSOP-Fq5EZ-XEW3fjUBdfnqUCIldW_BZWMo913L_eJuNFdkYb48dxU-L47loX';
		$auth_pass = 'EED3laQzQtZgjF-3-eqzuE_xQP6Ql_w-pfh6bOLT8l39ETeF3FXfwew8qRco5aVyYyuCeFZXmtj9znBu';		
		
		/* Init cURL resource */
		$ch = curl_init($url);

		/* Array Parameter Data */
		$data = array(
			'grant_type' => 'client_credentials'
		);

		/* pass encoded JSON string to the POST fields */
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

		/* set the content type json */
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			'Content-Type:application/x-www-form-urlencoded',
		));
		curl_setopt($ch, CURLOPT_USERPWD, $auth_name . ':' . $auth_pass);
		/* set return type json */
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);



		/* execute request */
		$result = curl_exec($ch);

		/* close cURL resource */
		curl_close($ch);

		// echo $result;

		$obj_login = json_decode($result);
		$access_token = $obj_login->access_token;
		return $access_token;
	}

}
