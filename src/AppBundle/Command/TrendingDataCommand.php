<?php

namespace AppBundle\Command;

use AppBundle\Entity\Poster;
use AppBundle\Entity\TrendingPoster;
use Exception;
use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use GuzzleHttp\Client;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

class TrendingDataCommand extends ContainerAwareCommand
{
    private $client;

    protected function configure()
    {
        $this
            ->setName('sync:daily:trendingData')
            ->setDescription('Sync trending data from TMDB api');

    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $em = $this->getContainer()->get('doctrine')->getManager();
        $start = microtime(true);
        $logFile = $this->getContainer()->get('kernel')->getLogDir() . '/tmdb_custom.log';
        $logger = new Logger('tmdb');
        $logger->pushHandler(new StreamHandler($logFile, Logger::INFO));
        $logger->info('TMDB trending data sync started');
        $this->client = $this->getContainer()->get('app.guzzle_client');
        $apiKeys = $this->getContainer()->getParameter('api_key_tmdb');
        $cnt = 1;
        $ids = [];
        try {
            for ($page = 1; $page <= 5; $page++) {
                if ($page > 1) {
                    sleep(1);
                }
                if ($cnt <= 10) {
                    $response = $this->client->request('GET', "/3/trending/all/day", [
                        'query' => [
                            'api_key' => $apiKeys,
                            'language' => 'he-IL',
                            'page' => $page
                        ]
                    ]);
                    $responseContent = (string)$response->getBody();
                    $data = json_decode($responseContent, true);
                    if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
                        $logger->info('JSON Decode Error: ' . json_last_error_msg());
                        $output->writeln("No results found.");

                    }
                    if (isset($data['results']) && is_array($data['results'])) {
                        foreach ($data['results'] as $key => $item) {
                            $mediaType = $item['media_type'] ?? false;
                            if (in_array($mediaType, ['movie', 'tv'])) {
                                if ($mediaType == 'tv') {
                                    $mediaType = 'serie';
                                    $title = $item['name'] ?? false;

                                } else {
                                    $title = $item['title'] ?? false;
                                }
                                if ($title && $cnt <= 10) {
                                    if (!isset($ids[$item['id']])) {
                                        $posters = $em->getRepository("AppBundle:Poster")->findBy([
                                            'title' => $title,
                                            'type' => $mediaType,
                                        ]);
                                        $isFirst = 0;
                                        $objCurrentPoster = null;
                                        if (count($posters) > 0) {
                                            if (count($posters) > 1) {
                                                foreach ($posters as $poster) {
                                                    if ($poster->getFirst()) {
                                                        $isFirst = 1;
                                                        $objCurrentPoster = $poster;
                                                        break;
                                                    }
                                                }
                                            } else {
                                                $objCurrentPoster = $posters[0];
                                            }
                                        }
                                        if ($objCurrentPoster instanceof Poster) {
                                            $objTrendingPoster = new TrendingPoster();
                                            $objTrendingPoster->setPoster($objCurrentPoster);
                                            if ($isFirst) {
                                                $objTrendingPoster->setSortOrder(0);
                                            } else {
                                                $objTrendingPoster->setSortOrder($cnt);
                                            }
                                            $em->persist($objTrendingPoster);
                                            $cnt++;
                                        }
                                    }
                                    $ids[] = $item['id'];
                                }
                            }
                        }
                        $em->flush();
                        $lastTen = $em->getRepository("AppBundle:TrendingPoster")
                            ->findBy([], ['id' => 'DESC'], 10);
                        $idsToKeep = array_map(function ($poster) {
                            return $poster->getId();
                        }, $lastTen);

                        $qb = $em->createQueryBuilder();
                        $qb->select('tp')
                            ->from('AppBundle:TrendingPoster', 'tp')
                            ->where($qb->expr()->notIn('tp.id', ':ids'))
                            ->setParameter('ids', $idsToKeep);

                        $toDelete = $qb->getQuery()->getResult();
                        foreach ($toDelete as $poster) {
                            $em->remove($poster);
                        }
                        $em->flush();
                    } else {
                        $logger->info("No results found.");
                        $output->writeln("No results found.");
                    }
                    $logger->info('TMDB sync finished successfully');
                    $output->writeln('TMDB sync finished successfully');
                }
            }

        } catch (Exception $exception) {
            $errorMessage = 'Error during TMDB sync: ' . $exception->getMessage();
            $logger->info("error =>" . $errorMessage);
            $output->writeln("error =>" . $errorMessage);
        }
        $end = microtime(true);
        $time = $end - $start;
        $logger->info("Time ::" . $time);
    }

}
