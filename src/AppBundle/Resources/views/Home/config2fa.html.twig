{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<div class="container-fluid">
    <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="tab-moivie">
                        <a href="{{path("app_home_settings")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">settings</i> Settings</a>
                        <a href="{{path("app_home_barcode")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">payments</i> Payment</a>
                        <a href="{{path("app_home_forgot_form")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">payments</i> Forgot password details</a>
                        <a href="{{path("app_home_ads")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">monetization_on</i> Ads Settings</a>
                        <a href="{{path("app_mfa")}}" class="btn btn-tab-movie-active col-md-12"><i class="material-icons">lock</i> 2FA</a>
                        <a href="{{path("change_password")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">lock</i> Change password</a>
                    </div>
                </div>
            </div>

       <div class="col-md-9">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">lock</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">2FA Setting</h4>
                        <br>
                        <div class="text-center" style="width: 100%;">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="mfaCheckbox" {% if setting.enable2fa %}checked{% endif %}>
                                <label class="form-check-label" for="mfaCheckbox">Enable 2FA</label>
                            </div>
                        </div>
                        <div class="fileinput fileinput-new text-center" style="width: 100%;">
                            <div class="form-group label-floating">
                                <label class="control-label"><b>Scan QR code</b></label>
                            </div>
                            <div class="fileinput-new thumbnail" style=" width: 100%;">
                                <img  id="img-preview" src="{{grCodeUri}}"  style="width: 200px;">
                            </div>
                        </div>
                        <span class="pull-right"><a href="/" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Back</a></span>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $('#mfaCheckbox').on('change', function(){
        var status = $(this).prop('checked') ? 1 : 0;
        $.ajax({
                type: "GET",
                data: {status: status},
                url: "/2fa/status",
                success: function(data) {
                    $('body').find('.alert-dashborad').remove();
                    var message = '';
                    if(data.status){
                        message = '2FA enabled sucessfully.';
                    }else{
                        message = '2FA disabled sucessfully.';
                    }
                    $('body').prepend('<div class="alert alert-success alert-with-icon alert-dashborad" data-notify="container"  style="position: absolute;right: 20px;top: 0px;z-index: 1000;width: 18%;"><i class="material-icons" data-notify="icon">notifications</i><span data-notify="message">' + message + '</span></div>');
                }
            });
    });
</script>
{% endblock%}