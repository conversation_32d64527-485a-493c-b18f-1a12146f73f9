{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<div class="container-fluid">
    {%  if is_granted('ROLE_ADMIN') %}
        <div class="row">
            <div class="col-lg-4 col-md-4 col-sm-4">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">install_desktop</i>
                    </div>
                    <div class="card-content">
                        <p class="category">New Version users ({{version.code}})</p>
                        <h3 class="title">{{installed_count}}</h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">group</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Old Version Users</p>
                        <h3 class="title">{{notinstalled_count}}</h3>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-4 col-sm-4">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">groups_2</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Total Users</p>
                        <h3 class="title">{{users_count}}</h3>
                    </div>
                </div>
            </div>
        </div>
        <hr style="border-top: 1px dashed red;">
    {% endif %}
    <div class="row">
        {%  if is_granted('ROLE_ADMIN') %}
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">movie</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Movies</p>
                        <h3 class="title">{{movies_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_movie_index")}}">Movies list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">tv</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Series</p>
                        <h3 class="title">{{series_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_serie_index")}}">Series TV list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">live_tv</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Channels TV</p>
                        <h3 class="title">{{channels_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_channel_index")}}">Channels TV list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">view_list</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Categories</p>
                        <h3 class="title">{{category_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_category_index")}}">Categories list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">label</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Genre</p>
                        <h3 class="title">{{genre_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_genre_index")}}">Genres list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">flag</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Languages</p>
                        <h3 class="title">{{language_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_language_index")}}">Channels TV list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">public</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Countries</p>
                        <h3 class="title">{{country_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_country_index")}}">Countries list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">recent_actors</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Actores</p>
                        <h3 class="title">{{actor_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_actor_index")}}">Actors list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">slideshow</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Slides</p>
                        <h3 class="title">{{slide_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_slide_index")}}">Slides list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">remove_red_eye</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Movies Views</p>
                        <h3 class="title">{{movie_views}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_movie_index")}}">Movies list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">cloud_download</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Movies Downloads</p>
                        <h3 class="title">{{movie_downloads}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_movie_index")}}">Movies list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">share</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Movies Shares</p>
                        <h3 class="title">{{movie_shares}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_movie_index")}}">Movies list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">remove_red_eye</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Series Views</p>
                        <h3 class="title">{{serie_views}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_serie_index")}}">Series list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">cloud_download</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Series Downloads</p>
                        <h3 class="title">{{serie_downloads}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_serie_index")}}">Series list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">share</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Series Shares</p>
                        <h3 class="title">{{serie_shares}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_serie_index")}}">Series list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">remove_red_eye</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Channels Views</p>
                        <h3 class="title">{{channel_views}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_channel_index")}}">Channels list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">share</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Channels Shares</p>
                        <h3 class="title">{{channel_shares}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_channel_index")}}">Channels list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">comment</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Comments</p>
                        <h3 class="title">{{comment_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_comment_index")}}">Comments list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">message</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Support and Rating</p>
                        <h3 class="title">{{support_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_support_index")}}">Support list</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="red">
                        <i class="material-icons">devices_other</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Installs</p>
                        <h3 class="title">{{devices_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">perm_device_information</i><span> Application install</span> 
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 col-sm-6">
                <div class="card card-stats">
                    <div class="card-header" data-background-color="black">
                        <i class="material-icons">info</i>
                    </div>
                    <div class="card-content">
                        <p class="category">Version</p>
                        <h3 class="title">{{version_count}}</h3>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <i class="material-icons">keyboard_arrow_right</i><a href="{{path("app_version_index")}}">Versions list</a>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="col-lg-6 col-md-12 col-sm-12">
                <div class="card card-stats">
                    <div class="card-content">
                        <div class="category"><h3 class="title" style="float:left">PENDING</h3> <h3 class="title" style="float:right">{{pendingTasks|length}}</h3></div>
                        {% for task in pendingTasks %}
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6" style="margin-top: 10px; text-align: left; padding-left:25px">
                                                <span class="label label-{{task.getStatusColor() }}" >{{task.getStatusText() }}</span>
                                            </div>
                                            <div class="col-md-6" style="text-align: right; padding-right: 25px;">
                                                <a href="{{path('app_workertask_move', {'id': task.id, 'status': 3})}}" rel="tooltip" data-placement="left" class=" btn btn-success btn-xs btn-round" data-original-title="Move to Completed">
                                                    <i class="material-icons">task_alt</i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="card-text details" style="margin: 10px;">{{ task.details|raw }}</div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12 col-sm-12">
                <div class="card card-stats">
                    <div class="card-content">
                        <div class="category"><h3 class="title" style="float:left">COMPLETED</h3> <h3 class="title" style="float:right">{{completedTasks|length}}</h3></div>
                        {% for task in completedTasks %}
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6" style="margin-top: 10px; text-align: left; padding-left:25px">
                                                <span class="label label-{{task.getStatusColor() }}" >{{task.getStatusText() }}</span>
                                            </div>
                                            <div class="col-md-6" style="text-align: right; padding-right: 25px;">
                                                <a href="{{path('app_workertask_move', {'id': task.id, 'status': 1})}}" rel="tooltip" data-placement="left" class=" btn btn-danger btn-xs btn-round" data-original-title="Move to Pending">
                                                    <i class="material-icons">schedule</i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="card-text details" style="margin: 10px;">{{ task.details|raw }}</div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock%}