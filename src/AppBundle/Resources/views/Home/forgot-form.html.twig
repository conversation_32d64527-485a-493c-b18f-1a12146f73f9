{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<script src="https://cdn.tiny.cloud/1/o6xid8u3x05byna9k1qy074m1lvnjcu1h450gdkpzc9kok13/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<div class="container-fluid">
    <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="tab-moivie">
                        <a href="{{path("app_home_settings")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">settings</i> Settings</a>
                        <a href="{{path("app_home_barcode")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">payments</i> Payment</a>
                        <a href="{{path("app_home_forgot_form")}}" class="btn btn-tab-movie-active col-md-12"><i class="material-icons">payments</i> Forgot password details</a>
                        <a href="{{path("app_home_ads")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">monetization_on</i> Ads Settings</a>
                        <a href="{{path("app_mfa")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">lock</i> 2FA</a>
                        <a href="{{path("change_password")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">lock</i> Change password</a>
                    </div>
                </div>
            </div>

       <div class="col-md-9">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">payments</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Forgot password setting</h4>
                    {{form_start(form)}}
                        <br>
                        
                        <div class="fileinput fileinput-new text-center" style="    width: 100%;" data-provides="fileinput">
                            <div class="fileinput-new thumbnail" style="    width: 100%;">
                                {% if form.vars.value.forgotMedia and form.vars.value.forgotMedia.link %}
                                <img  id="img-preview" src="{{asset(form.vars.value.forgotMedia.link)}}"  style="width: 200px;">
                                {% endif %}
                                
                                <a href="#" class="btn btn-rose btn-round btn-select"><i class="material-icons">image</i> Select barcode </a>
                            </div>
                           {{form_widget(form.barcode,{"attr":{"class":"file-hidden input-file img-selector","style":"    display: none;"}})}}
                            <span class="validate-input">{{form_errors(form.barcode)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Button name : </label>
                            {{form_widget(form.forgotButton,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.forgotButton)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Header : </label>
                            {{form_widget(form.forgotText,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.forgotText)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Link : </label>
                            {{form_widget(form.forgotLink,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.forgotLink)}}</span>
                        </div>
                        <span class="pull-right"><a href="/" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>
<script>
  tinymce.init({
    selector: 'textarea#forgot_forgotText',
    toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
    content_style: 'body {background-color: black }'
  });
</script>
{% endblock%}