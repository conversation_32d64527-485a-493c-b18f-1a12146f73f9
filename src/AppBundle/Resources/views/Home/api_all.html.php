<?php 
$obj;
$channels_list =  array();
$slides_list =  array();
$genres_list =  array();
$genres_list =  array();
$base_url = 'https://admin.nachosisrael.com/';

foreach ($channels as $key => $channel) {
	$ch = null;
	$ch["id"]=$channel->getId();
	$ch["title"]=$channel->getTitle();
	$ch["description"]=$channel->getDescription();
	$ch["website"]=$channel->getWebsite();
	$ch["classification"]=$channel->getClassification();
	$ch["views"]=$channel->getViews();
	$ch["shares"]=$channel->getShares();
	$ch["rating"]=$channel->getRating();
	$ch["playas"]=$channel->getPlayas();
	$ch["comment"]=$channel->getComment();
	$ch["image"] = $this['imagine']->filter($view['assets']->getUrl($channel->getMedia()->getLink()), 'channel_thumb');
	$source_channel_list = array();
	foreach ($channel->getSources() as $key => $source_channel) {
		$source_channel_obj = array();
		$source_channel_obj["id"]=$source_channel->getId();
		
		$source_channel_obj["type"]=$source_channel->getType();
		$source_channel_obj["url"]=$source_channel->getUrl();
		
		$source_channel_list[] = $source_channel_obj;
	}
	$ch["sources"] = $source_channel_list;

	$category_channel_list = array();
	foreach ($channel->getCategories() as $key => $category_channel) {
		$category_channel_obj = array();
		$category_channel_obj["id"]=$category_channel->getId();
		$category_channel_obj["title"]=$category_channel->getTitle();		
		$category_channel_list[] = $category_channel_obj;
	}
	$ch["categories"] = $category_channel_list;

	$country_channel_list = array();
	foreach ($channel->getCountries() as $key => $country_channel) {
		$country_channel_obj = array();
		$country_channel_obj["id"]=$country_channel->getId();
		$country_channel_obj["title"]=$country_channel->getTitle();		
		$country_channel_obj["image"] = $this['imagine']->filter($view['assets']->getUrl($country_channel->getMedia()->getLink()), 'country_thumb');

		$country_channel_list[] = $country_channel_obj;
	}
	$ch["countries"] = $country_channel_list;

	$channels_list[]=$ch;
}
foreach ($slides as $key => $slide) {
	$slide_obj = null;
	$slide_obj["id"]=$slide->getId();
	$slide_obj["title"]=$slide->getClear();
	$slide_obj["type"]=$slide->getType();
	$slide_obj["image"] = $this['imagine']->filter($view['assets']->getUrl($slide->getMedia()->getLink()), 'slide_thumb');
	if ($slide->getType() == "1" && $slide->getUrl() != null) {
		$slide_obj["url"]= $slide->getUrl();
	}
	if ($slide->getType() == "2" && $slide->getCategory() != null) {
		$category_obj = null;
		$category_obj["id"]=$slide->getCategory()->getId();
		$category_obj["title"]=$slide->getCategory()->getTitle();	
		$category_obj["image"] = $this['imagine']->filter($view['assets']->getUrl($slide->getCategory()->getMedia()->getLink()), 'country_thumb');
		$slide_obj["category"]= $category_obj;
	}
	if ($slide->getType() == "5" && $slide->getGenre() != null) {
		$genre_obj = null;
		$genre_obj["id"]=$slide->getGenre()->getId();
		$genre_obj["title"]=$slide->getGenre()->getTitle();
		$slide_obj["genre"]= $genre_obj;
	}
	if ($slide->getType() == "3" && $slide->getChannel() != null) {
		$ch = null;
		$ch["id"]=$slide->getChannel()->getId();
		$ch["title"]=$slide->getChannel()->getTitle();
		$ch["description"]=$slide->getChannel()->getDescription();
		$ch["website"]=$slide->getChannel()->getWebsite();
		$ch["classification"]=$slide->getChannel()->getClassification();
		$ch["views"]=$slide->getChannel()->getViews();
		$ch["playas"]=$slide->getChannel()->getPlayas();

		$ch["shares"]=$slide->getChannel()->getShares();
		$ch["rating"]=$slide->getChannel()->getRating();
		$ch["comment"]=$slide->getChannel()->getComment();
		$ch["image"] = $this['imagine']->filter($view['assets']->getUrl($slide->getChannel()->getMedia()->getLink()), 'channel_thumb');
		$source_channel_list = array();
		foreach ($slide->getChannel()->getSources() as $key => $source_channel) {
			$source_channel_obj = array();
			$source_channel_obj["id"]=$source_channel->getId();
			
			$source_channel_obj["type"]=$source_channel->getType();
			$source_channel_obj["url"]=$source_channel->getUrl();
			
			$source_channel_list[] = $source_channel_obj;
		}
		$ch["sources"] = $source_channel_list;

		$category_channel_list = array();
		foreach ($slide->getChannel()->getCategories() as $key => $category_channel) {
			$category_channel_obj = array();
			$category_channel_obj["id"]=$category_channel->getId();
			$category_channel_obj["title"]=$category_channel->getTitle();		
			$category_channel_list[] = $category_channel_obj;
		}
		$ch["categories"] = $category_channel_list;

		$country_channel_list = array();
		foreach ($channel->getCountries() as $key => $country_channel) {
			$country_channel_obj = array();
			$country_channel_obj["id"]=$country_channel->getId();
			$country_channel_obj["title"]=$country_channel->getTitle();		
			$country_channel_obj["image"] = $this['imagine']->filter($view['assets']->getUrl($country_channel->getMedia()->getLink()), 'country_thumb');

			$country_channel_list[] = $country_channel_obj;
		}
		$ch["countries"] = $country_channel_list;
		$slide_obj["channel"]= $ch;

	}
	if ($slide->getType() == "4" && $slide->getPoster() != null) {
		$pstr = null;
		$isNew = in_array($slide->getPoster()->getId(), $newContentIds);
		$pstr["id"]= $slide->getPoster()->getId();
		$pstr["title"]= $slide->getPoster()->getTitle();
		$pstr["type"]= $slide->getPoster()->getType();
		$pstr["description"]= $slide->getPoster()->getDescription();
		$pstr["year"]= $slide->getPoster()->getYear();
		$pstr["imdb"]= $slide->getPoster()->getImdb();
		$pstr["isNew"]= $isNew;

		$pstr["rating"]= $slide->getPoster()->getRating();
		$pstr["duration"] = $slide->getPoster()->getDuration();
		$pstr["downloadas"] = $slide->getPoster()->getDownloadas();
		$pstr["comment"] = $slide->getPoster()->getComment();
		$pstr["playas"] = $slide->getPoster()->getPlayas();	
		$pstr["classification"]= $slide->getPoster()->getClassification();
		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($slide->getPoster()->getPoster()->getLink()), 'poster_thumb');
		if($slide->getPoster()->getCover())
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($slide->getPoster()->getCover()->getLink()), 'cover_thumb');
	
		$genre_poster_list =  array();
		foreach ($slide->getPoster()->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"]=$genre_poster->getId();
			$genre_poster_obj["title"]=$genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if($slide->getPoster()->getTrailer()){
			$trailer_poster_obj["id"]=$slide->getPoster()->getTrailer()->getId();
			if ($slide->getPoster()->getTrailer()->getType()=="file") {
				$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $slide->getPoster()->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"]=$slide->getPoster()->getTrailer()->getMedia()->getExtension();

			}else{
				$trailer_poster_obj["type"]=$slide->getPoster()->getTrailer()->getType();
				$trailer_poster_obj["url"]=$slide->getPoster()->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($slide->getPoster()->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"]=$source_poster->getId();
			if ($source_poster->getType()=="file") {
				$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
				$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

			}else{
				$source_poster_obj["type"]=$source_poster->getType();
				$source_poster_obj["url"]=$source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;
		$slide_obj["poster"]= $pstr;
	}
	$slides_list[]=$slide_obj;
}

if($remindMe){
	$genre_obj = null;
	$genre_obj["id"]=-3;
	$genre_obj["title"]="Remind me";
	$posters = array();
	foreach ($remindMe as $key => $remind) {
			$poster = $remind->getPoster();
			$pstr = null;
			$pstr["id"]= $poster->getId();
			$pstr["title"]= $poster->getTitle();
			$pstr["type"]= $poster->getType();
			$pstr["imdb"]= $poster->getImdb();
			$pstr["isNew"]= true;
			$pstr["description"]= $poster->getDescription();
			$pstr["year"]= $poster->getYear();
			$pstr["comment"]= $poster->getComment();
			$pstr["rating"]= $poster->getRating();
			$pstr["duration"] = $poster->getDuration();
			$pstr["downloadas"] = $poster->getDownloadas();
			$pstr["playas"] = $poster->getPlayas();
			$pstr["classification"]= $poster->getClassification();
			$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
			if($poster->getCover()){
				$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
							if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
					$pstr["cover"] = $base_url.$poster->getCover()->getLink();
							}
				}

			$genre_poster_list =  array();
			foreach ($poster->getGenres() as $key => $genre_poster) {
				$genre_poster_obj = array();
				$genre_poster_obj["id"]=$genre_poster->getId();
				$genre_poster_obj["title"]=$genre_poster->getTitle();
				$genre_poster_list[] = $genre_poster_obj;
			}
			$pstr["genres"] = $genre_poster_list;

			if($poster->getTrailer()){
				$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
				if ($poster->getTrailer()->getType()=="file") {
					$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
					$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

				}else{
					$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
					$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
				}
				$pstr["trailer"] = $trailer_poster_obj;
			}

			$source_poster_list =  array();
			foreach ($poster->getSources() as $key => $source_poster) {
				$source_poster_obj = array();
				$source_poster_obj["id"]=$source_poster->getId();
				if ($source_poster->getType()=="file") {
					$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
					$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

				}else{
					$source_poster_obj["type"]=$source_poster->getType();
					$source_poster_obj["url"]=$source_poster->getUrl();
				}
				$source_poster_list[] = $source_poster_obj;
			}
			$pstr["sources"] = $source_poster_list;

			$posters[]=$pstr;
	}
	$genre_obj["posters"] = $posters;
	$genres_list[]=$genre_obj;
}

if ($continue_watch != null || $continue_watch_episode != null ) {
	$genre_obj = null;
	$genre_obj["id"] = -2;
	$genre_obj["title"] = "המשך צפייה";
	$posters = array();
	// $posters = $continue_watch;

	// echo json_encode($continue_watch);
	foreach ($continue_watch as $key => $poster) {
		// echo json_encode($poster->getEmail());
		// echo json_encode($poster->getPoster()->getTitle());
		$isNew = in_array($poster->getId(), $newContentIds);
        if( $poster->getPercentage() != 100 && $poster->getMiliSecond() > 60000 ){
		$pstr = null;

		$pstr["id"] = $poster->getPoster()->getId();
		$pstr["mili"] = $poster->getMiliSecond();
		$pstr["percentage"] = $poster->getPercentage();
		$pstr["title"] = $poster->getPoster()->getTitle();
		$pstr["type"] = $poster->getPoster()->getType();
		$pstr["description"] = $poster->getPoster()->getDescription();
		$pstr["year"] = $poster->getPoster()->getYear();
		$pstr["imdb"] = $poster->getPoster()->getImdb();
		$pstr["isNew"] = $isNew;
		$pstr["rating"] = $poster->getPoster()->getRating();
		$pstr["comment"] = $poster->getPoster()->getComment();
		$pstr["duration"] = $poster->getPoster()->getDuration();
		$pstr["downloadas"] = $poster->getPoster()->getDownloadas();
		$pstr["playas"] = $poster->getPoster()->getPlayas();
		$pstr["classification"] = $poster->getPoster()->getClassification();
		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getPoster()->getLink()), 'poster_thumb');
		$pstr["last_watch"] = $poster->getLastwatch(); 


		if ($poster->getPoster()->getCover()){
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getCover()->getLink()), 'cover_thumb');
                         if (file_exists(str_replace('/\s+/', '', $poster->getPoster()->getCover()->getLink()))){	 
		        $pstr["cover"] = $base_url.$poster->getPoster()->getCover()->getLink();
                         }
                }

		$genre_poster_list =  array();
		foreach ($poster->getPoster()->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"] = $genre_poster->getId();
			$genre_poster_obj["title"] = $genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if ($poster->getPoster()->getTrailer()) {
			$trailer_poster_obj["id"] = $poster->getPoster()->getTrailer()->getId();
			if ($poster->getPoster()->getTrailer()->getType() == "file") {
				$trailer_poster_obj["url"] = $app->getRequest()->getScheme() . "://" . $app->getRequest()->getHttpHost() . "/" . $poster->getPoster()->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"] = $poster->getPoster()->getTrailer()->getMedia()->getExtension();
			} else {
				$trailer_poster_obj["type"] = $poster->getPoster()->getTrailer()->getType();
				$trailer_poster_obj["url"] = $poster->getPoster()->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($poster->getPoster()->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"] = $source_poster->getId();
			if ($source_poster->getType() == "file") {
				$source_poster_obj["url"] = $app->getRequest()->getScheme() . "://" . $app->getRequest()->getHttpHost() . "/" . $source_poster->getMedia()->getLink();
				$source_poster_obj["type"] = $source_poster->getMedia()->getExtension();
			} else {
				$source_poster_obj["type"] = $source_poster->getType();
				$source_poster_obj["url"] = $source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;

		$posters[] = $pstr;
        }
	}

   	
	// echo json_encode("this is test</br></br>");
	// echo json_encode($continue_watch_episode[0]->getEpisode()->getSeason()->getPoster()->getId());


	foreach ($continue_watch_episode as $key => $episode) {// לופ על סדרות שנצפו ומסודרות לפי תאריך צפיה
		// echo json_encode($episode->getEmail());
		// echo json_encode($episode->getPoster()->getTitle());
		
    // מכניס רק פרק אחד מכל סדרה את הפרק האחרון שנצפה

    $last_poster[] = 0;
    if (!in_array($episode->getEpisode()->getSeason()->getPoster()->getId(), $last_poster)){
        $last_poster[] = $episode->getEpisode()->getSeason()->getPoster()->getId();
 
		$pstr = null;
		$isNew = in_array($episode->getEpisode()->getSeason()->getPoster()->getId(), $newContentIds);
		$pstr["id"] = $episode->getEpisode()->getSeason()->getPoster()->getId();
 		$pstr["season_id"] = $episode->getEpisode()->getSeason()->getId();
 		$pstr["season_title"] = $episode->getEpisode()->getSeason()->getTitle();
 		$pstr["poster_title"] = $episode->getEpisode()->getSeason()->getPoster()->getTitle();
		$pstr["episode_id"] = $episode->getEpisode()->getId();
		$pstr["mili"] = $episode->getMiliSecond();
		$pstr["percentage"] = $episode->getPercentage();
		$pstr["title"] =$pstr["season_title"] .' '. $episode->getEpisode()->getTitle();
		$pstr["type"] = $episode->getEpisode()->getSeason()->getPoster()->getType();
		$pstr["description"] = $episode->getEpisode()->getSeason()->getPoster()->getDescription();
		$pstr["year"] = $episode->getEpisode()->getSeason()->getPoster()->getYear();
		$pstr["imdb"] = $episode->getEpisode()->getSeason()->getPoster()->getImdb();
		$pstr["isNew"] = $isNew;
		$pstr["rating"] = $episode->getEpisode()->getSeason()->getPoster()->getRating();
		$pstr["comment"] = $episode->getEpisode()->getSeason()->getPoster()->getComment();
		$pstr["duration"] = $episode->getEpisode()->getDuration();
		$pstr["downloadas"] = $episode->getEpisode()->getDownloadas();
		$pstr["playas"] = $episode->getEpisode()->getSeason()->getPoster()->getPlayas();
		$pstr["classification"] = $episode->getEpisode()->getSeason()->getPoster()->getClassification();
		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($episode->getEpisode()->getSeason()->getPoster()->getPoster()->getLink()), 'poster_thumb');
		$pstr["last_watch"] = $episode->getLastwatch(); 
		


		if ($episode->getEpisode()->getSeason()->getPoster()->getCover()){
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($episode->getEpisode()->getSeason()->getPoster()->getCover()->getLink()), 'cover_thumb');
                         if (file_exists(str_replace('/\s+/', '', $episode->getEpisode()->getSeason()->getPoster()->getCover()->getLink()))){	 
		        $pstr["cover"] = $base_url.$episode->getEpisode()->getSeason()->getPoster()->getCover()->getLink();
                         }
                }

		$genre_poster_list =  array();
		foreach ($episode->getEpisode()->getSeason()->getPoster()->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"] = $genre_poster->getId();
			$genre_poster_obj["title"] = $genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if ($episode->getEpisode()->getSeason()->getPoster()->getTrailer()) {
			$trailer_poster_obj["id"] = $episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getId();
			if ($episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getType() == "file") {
				$trailer_poster_obj["url"] = $app->getRequest()->getScheme() . "://" . $app->getRequest()->getHttpHost() . "/" . $episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"] = $episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getMedia()->getExtension();
			} else {
				$trailer_poster_obj["type"] = $episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getType();
				$trailer_poster_obj["url"] = $episode->getEpisode()->getSeason()->getPoster()->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($episode->getEpisode()->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"] = $source_poster->getId();
			if ($source_poster->getType() == "file") {
				$source_poster_obj["url"] = $app->getRequest()->getScheme() . "://" . $app->getRequest()->getHttpHost() . "/" . $source_poster->getMedia()->getLink();
				$source_poster_obj["type"] = $source_poster->getMedia()->getExtension();
			} else {
				$source_poster_obj["type"] = $source_poster->getType();
				$source_poster_obj["url"] = $source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;

        
		$posters_episode[] = $pstr;

     	}
	}
  
   if(isset($posters_episode)){
    $posters_episode = array_reverse($posters_episode);// רוורס מערך סדרות 
    $posters = array_reverse($posters);// רוורס מערך סרטים     
    $posters  = array_merge($posters,$posters_episode);// איחוד מערך סרטים ומערך סדרות 
   }else{
    $posters  = $posters;       
   }

        usort($posters, function ($a, $b) {
         return $b['last_watch'] <=> $a['last_watch'] ;
        }); 	
        
    // $posters = array_reverse($posters);// רוורס מערך סופי המשך צפייה
    
	$genre_obj["posters"] = $posters;
	
	$genres_list[] = $genre_obj;
}


$genre_obj = null;
$genre_obj["id"]=-1;
$genre_obj["title"]="דירוג גבוה";
$posters = array();

foreach ($bestrated as $key => $poster) {
		$isNew = in_array($poster->getId(), $newContentIds);
		$pstr = null;
		$pstr["id"]= $poster->getId();
		$pstr["title"]= $poster->getTitle();
		$pstr["type"]= $poster->getType();
		$pstr["description"]= $poster->getDescription();
		$pstr["year"]= $poster->getYear();
		$pstr["imdb"]= $poster->getImdb();
		$pstr["isNew"]= $isNew;
		$pstr["rating"]= $poster->getRating();
		$pstr["comment"]= $poster->getComment();
		$pstr["duration"] = $poster->getDuration();
		$pstr["downloadas"] = $poster->getDownloadas();
		$pstr["playas"] = $poster->getPlayas();	
		$pstr["classification"]= $poster->getClassification();
		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
		if($poster->getCover()){
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
                         if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
		        $pstr["cover"] = $base_url.$poster->getCover()->getLink();
                         }
		}

		$genre_poster_list =  array();
		foreach ($poster->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"]=$genre_poster->getId();
			$genre_poster_obj["title"]=$genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if($poster->getTrailer()){
			$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
			if ($poster->getTrailer()->getType()=="file") {
				$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

			}else{
				$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
				$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($poster->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"]=$source_poster->getId();
			if ($source_poster->getType()=="file") {
				$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
				$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

			}else{
				$source_poster_obj["type"]=$source_poster->getType();
				$source_poster_obj["url"]=$source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;

		$posters[]=$pstr;
	

}

//$genre_obj["posters"] = $posters;
//$genres_list[]=$genre_obj;
$genre_obj = null;
$genre_obj["id"]=0;
$genre_obj["title"]="פופלרי";
$posters = array();
foreach ($popular as $key => $poster) {
		$isNew = in_array($poster->getId(), $newContentIds);
		$pstr = null;
		$pstr["id"]= $poster->getId();
		$pstr["title"]= $poster->getTitle();
		$pstr["type"]= $poster->getType();
		$pstr["imdb"]= $poster->getImdb();
		$pstr["isNew"]= $isNew;
		$pstr["description"]= $poster->getDescription();
		$pstr["year"]= $poster->getYear();
		$pstr["comment"]= $poster->getComment();
		$pstr["rating"]= $poster->getRating();
		$pstr["duration"] = $poster->getDuration();
		$pstr["downloadas"] = $poster->getDownloadas();
		$pstr["playas"] = $poster->getPlayas();
		$pstr["classification"]= $poster->getClassification();
		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
		if($poster->getCover()){
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
                          if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
		         $pstr["cover"] = $base_url.$poster->getCover()->getLink();
                         }
	        }

		$genre_poster_list =  array();
		foreach ($poster->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"]=$genre_poster->getId();
			$genre_poster_obj["title"]=$genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if($poster->getTrailer()){
			$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
			if ($poster->getTrailer()->getType()=="file") {
				$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

			}else{
				$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
				$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($poster->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"]=$source_poster->getId();
			if ($source_poster->getType()=="file") {
				$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
				$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

			}else{
				$source_poster_obj["type"]=$source_poster->getType();
				$source_poster_obj["url"]=$source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;

		$posters[]=$pstr;
	

}
$genre_obj["posters"] = $posters;
$genres_list[]=$genre_obj;

shuffle($genres);
$count_genres = 0;
foreach ($genres as $key => $genre) {
    if($count_genres<7){
		$count_genres++;
		$genre_obj = null;
		$genre_obj["id"]=$genre->getId();
		$genre_obj["title"]=$genre->getTitle();
		$posters = array();
		$count = 0;
		$x = 0;
		
		$posters_unshuffled= $genre->getPosters();
		
		// foreach ($posters_unshuffled as $value) {
		// $posters_shuffled[$x] = $posters_unshuffled[$x];
		// $x++;
		// }
		//shuffle($posters_shuffled);

		foreach ($posters_unshuffled as $key => $poster) {
			if ($count<15 && $poster->getEnabled()) {	    
				$count++;
				$pstr = null;
				$isNew = in_array($poster->getId(), $newContentIds);
				$pstr["id"]= $poster->getId();
				$pstr["title"]= $poster->getTitle();
				$pstr["type"]= $poster->getType();
				$pstr["description"]= $poster->getDescription();
				$pstr["year"]= $poster->getYear();
				$pstr["rating"]= $poster->getRating();
				$pstr["comment"]= $poster->getComment();
				$pstr["imdb"]= $poster->getImdb();
				$pstr["isNew"]= $isNew;
				$pstr["duration"] = $poster->getDuration();
				$pstr["downloadas"] = $poster->getDownloadas();
				$pstr["playas"] = $poster->getPlayas();
				$pstr["classification"]= $poster->getClassification();
				$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
				if($poster->getCover()){
					$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
									if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
							$pstr["cover"] = $base_url.$poster->getCover()->getLink();
									}
					}

				$genre_poster_list =  array();
				foreach ($poster->getGenres() as $key => $genre_poster) {
					$genre_poster_obj = array();
					$genre_poster_obj["id"]=$genre_poster->getId();
					$genre_poster_obj["title"]=$genre_poster->getTitle();
					$genre_poster_list[] = $genre_poster_obj;
				}

				$pstr["genres"] = $genre_poster_list;

				if($poster->getTrailer()){
					$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
					if ($poster->getTrailer()->getType()=="file") {
						$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
						$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

					}else{
						$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
						$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
					}
					$pstr["trailer"] = $trailer_poster_obj;
				}

				$source_poster_list =  array();
				foreach ($poster->getSources() as $key => $source_poster) {
					$source_poster_obj = array();
					$source_poster_obj["id"]=$source_poster->getId();
					if ($source_poster->getType()=="file") {
						$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
						$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

					}else{
						$source_poster_obj["type"]=$source_poster->getType();
						$source_poster_obj["url"]=$source_poster->getUrl();
					}
					$source_poster_list[] = $source_poster_obj;
				}
				$pstr["sources"] = $source_poster_list;

				$posters[]=$pstr;
			}

		}
		//shuffle($posters);
		$genre_obj["posters"] = $posters;
		$genres_list[]=$genre_obj;
	}
}
$actors_list=array();
foreach ($actors as $key => $actor) {
          $actor_obj["id"]=$actor["id"];
          $actor_obj["type"]=$actor["type"];
          $actor_obj["name"]=$actor["name"];
          $actor_obj["bio"]=$actor["bio"];
          $actor_obj["height"]=$actor["height"];
          $actor_obj["born"]=$actor["born"];
	$actor_obj["image"] = $this['imagine']->filter($view['assets']->getUrl("uploads/".$actor["extension"]."/".$actor["image"]), 'actor_thumb');
	$actors_list[]=$actor_obj;
}

$first_big_posters_list = array();
foreach ($first_big_posters as $key => $poster) {
	$pstr = null;		
	$isNew = in_array($poster->getId(), $newContentIds);
	$pstr["id"]= $poster->getId();
	$pstr["title"]= $poster->getTitle();
	$pstr["type"]= $poster->getType();
	$pstr["imdb"]= $poster->getImdb();
	$pstr["isNew"]= $isNew;
	$pstr["description"]= $poster->getDescription();
	$pstr["year"]= $poster->getYear();
	$pstr["created"]= date_format($poster->getCreated(),"Y/m/d H:i:s");
	$pstr["comment"]= $poster->getComment();
	$pstr["rating"]= $poster->getRating();
	$pstr["duration"] = $poster->getDuration();
	$pstr["downloadas"] = $poster->getDownloadas();
	$pstr["playas"] = $poster->getPlayas();
	$pstr["classification"]= $poster->getClassification();
	 $pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
	if (file_exists(str_replace('/\s+/', '', $poster->getPoster()->getLink()))){ 		    
	$pstr["image"] = $base_url.$poster->getPoster()->getLink();
	 }
	if($poster->getCover()){
		$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
	if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
		$pstr["cover"] = $base_url.$poster->getCover()->getLink();
	}
	}

	$genre_poster_list =  array();
	foreach ($poster->getGenres() as $key => $genre_poster) {
		$genre_poster_obj = array();
		$genre_poster_obj["id"]=$genre_poster->getId();
		$genre_poster_obj["title"]=$genre_poster->getTitle();
		$genre_poster_list[] = $genre_poster_obj;
	}
	$pstr["genres"] = $genre_poster_list;

	if($poster->getTrailer()){
		$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
		if ($poster->getTrailer()->getType()=="file") {
			$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
			$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

		}else{
			$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
			$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
		}
		$pstr["trailer"] = $trailer_poster_obj;
	}

	$source_poster_list =  array();
	foreach ($poster->getSources() as $key => $source_poster) {
		$source_poster_obj = array();
		$source_poster_obj["id"]=$source_poster->getId();
		if ($source_poster->getType()=="file") {
			$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
			$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

		}else{
			$source_poster_obj["type"]=$source_poster->getType();
			$source_poster_obj["url"]=$source_poster->getUrl();
		}
		$source_poster_list[] = $source_poster_obj;
	}
	$pstr["sources"] = $source_poster_list;

	$first_big_posters_list[]=$pstr;

}


$big_posters_list = array();

foreach ($big_posters as $key => $poster) {
		$pstr = null;
		$isNew = in_array($poster->getId(), $newContentIds);
     //if($poster->getYear() == "2023"){		
		$pstr["id"]= $poster->getId();
		$pstr["title"]= $poster->getTitle();
		$pstr["type"]= $poster->getType();
		$pstr["imdb"]= $poster->getImdb();
		$pstr["isNew"]= $isNew;
		$pstr["description"]= $poster->getDescription();
		$pstr["year"]= $poster->getYear();
		$pstr["created"]= date_format($poster->getCreated(),"Y/m/d H:i:s");
		$pstr["comment"]= $poster->getComment();
		$pstr["rating"]= $poster->getRating();
		$pstr["duration"] = $poster->getDuration();
		$pstr["downloadas"] = $poster->getDownloadas();
		$pstr["playas"] = $poster->getPlayas();
		$pstr["classification"]= $poster->getClassification();
 		$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
        if (file_exists(str_replace('/\s+/', '', $poster->getPoster()->getLink()))){ 		    
		$pstr["image"] = $base_url.$poster->getPoster()->getLink();
 		}
		if($poster->getCover()){
			$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');
        if (file_exists(str_replace('/\s+/', '', $poster->getCover()->getLink()))){	 
			$pstr["cover"] = $base_url.$poster->getCover()->getLink();
        }
		}
	
		$genre_poster_list =  array();
		foreach ($poster->getGenres() as $key => $genre_poster) {
			$genre_poster_obj = array();
			$genre_poster_obj["id"]=$genre_poster->getId();
			$genre_poster_obj["title"]=$genre_poster->getTitle();
			$genre_poster_list[] = $genre_poster_obj;
		}
		$pstr["genres"] = $genre_poster_list;

		if($poster->getTrailer()){
			$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
			if ($poster->getTrailer()->getType()=="file") {
				$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
				$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

			}else{
				$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
				$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
			}
			$pstr["trailer"] = $trailer_poster_obj;
		}

		$source_poster_list =  array();
		foreach ($poster->getSources() as $key => $source_poster) {
			$source_poster_obj = array();
			$source_poster_obj["id"]=$source_poster->getId();
			if ($source_poster->getType()=="file") {
				$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
				$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

			}else{
				$source_poster_obj["type"]=$source_poster->getType();
				$source_poster_obj["url"]=$source_poster->getUrl();
			}
			$source_poster_list[] = $source_poster_obj;
		}
		$pstr["sources"] = $source_poster_list;
   
		$big_posters_list[]=$pstr;
		shuffle($big_posters_list);
        // usort($big_posters_list, function ($a, $b) {
        //  return $b['created'] <=> $a['created'] ;
        // }); 		
     //}

}


//  print_r($posters);

$obj["channels"]=$channels_list;
// $obj["slides"]=$slides_list;
$obj["bigPosters"]=$first_big_posters_list + $big_posters_list;
$obj["genres"]=$genres_list;
$obj["actors"]=$actors_list;
echo json_encode($obj, JSON_UNESCAPED_UNICODE);
?>