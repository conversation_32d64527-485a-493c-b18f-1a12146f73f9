{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<script src="https://cdn.tiny.cloud/1/o6xid8u3x05byna9k1qy074m1lvnjcu1h450gdkpzc9kok13/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<div class="container-fluid">
    <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="tab-moivie">
                        <a href="{{path("app_home_settings")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">settings</i> Settings</a>
                        <a href="{{path("app_home_barcode")}}" class="btn btn-tab-movie-active col-md-12"><i class="material-icons">payments</i> Payment</a>
                        <a href="{{path("app_home_forgot_form")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">payments</i> Forgot password details</a>
                        <a href="{{path("app_home_ads")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">monetization_on</i> Ads Settings</a>
                        <a href="{{path("app_mfa")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">lock</i> 2FA</a>
                        <a href="{{path("change_password")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">lock</i> Change password</a>
                    </div>
                </div>
            </div>

       <div class="col-md-9">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">payments</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Payment setting</h4>
                    {{form_start(form)}}
                        <br>
                        
                        <div class="fileinput fileinput-new text-center" style="    width: 100%;" data-provides="fileinput">
                            <div class="fileinput-new thumbnail" style="    width: 100%;">
                                {% if form.vars.value.barcodeMedia and form.vars.value.barcodeMedia.link %}
                                <img  id="img-preview" src="{{asset(form.vars.value.barcodeMedia.link)}}"  style="width: 200px;">
                                {% endif %}
                                
                                <a href="#" class="btn btn-rose btn-round btn-select"><i class="material-icons">image</i> Select barcode </a>
                            </div>
                           {{form_widget(form.barcode,{"attr":{"class":"file-hidden input-file img-selector","style":"    display: none;"}})}}
                            <span class="validate-input">{{form_errors(form.barcode)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Banner Title : </label>
                            {{form_widget(form.bannerTitle,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.bannerTitle)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Barcode Title : </label>
                            {{form_widget(form.barcodeTitle,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.barcodeTitle)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Barcode Under Text : </label>
                            {{form_widget(form.barcodeUnderText,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.barcodeUnderText)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Right Title : </label>
                            {{form_widget(form.rightTitle,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.rightTitle)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Right subtitle : </label>
                            {{form_widget(form.rightSubtitle,{"attr":{"class":"form-control","rows":8}})}}
                            <span class="validate-input">{{form_errors(form.rightSubtitle)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Text 1 : </label>
                            {{form_widget(form.text1,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.text1)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Text 2 : </label>
                            {{form_widget(form.text2,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.text2)}}</span>
                        </div>
                        <div class="form-group label-floating">
                            <label class="control-label">Text 3: </label>
                            {{form_widget(form.text3,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.text3)}}</span>
                        </div>
                        <span class="pull-right"><a href="/" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>
<script>
  tinymce.init({
    selector: 'textarea#payment_rightSubtitle',
    toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat',
    content_style: 'body {background-color: black }'
  });
</script>
{% endblock%}