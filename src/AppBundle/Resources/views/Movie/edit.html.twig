{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card card-stats" style="margin: 15px 0;">
					<div class="card-content views-body  pull-right">
						<a href="#" class="btn btn-tab-movie"><i class="material-icons">remove_red_eye</i> {{movie.viewsnumber}} Views</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">share</i> {{movie.sharesnumber}} Shares</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">cloud_download</i> {{movie.downloadsnumber}} Downloads</a>
					</div>
					<div class="card-header " data-background-color="green">
						<i class="material-icons">movie_filter</i>
					</div>
					<div class="card-content trailer-body">
						<h4 class="title">Edit {{movie.title }} </b></h4>
						<div class="tab-moivie">
							<a href="{{path("app_movie_edit",{"id":movie.id})}}" class="btn btn-tab-movie-active"><i class="material-icons">edit</i> Edit</a>
							<a href="{{path("app_movie_sources",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">folder</i> Sources</a>
							<a href="{{path("app_movie_cast",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">recent_actors</i> Cast</a>
							<a href="{{path("app_movie_trailer",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">movie_filter</i> Trailer</a>
							<a href="{{path("app_movie_subtitles",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">subtitles</i> Subtitles</a>
							<a href="{{path("app_movie_comments",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">comment</i> Comments</a>
							<a href="{{path("app_movie_ratings",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">star_half</i> Ratings</a>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12">
				<div class="card">
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">movie</i>
					</div>
					<div class="card-content">
						<h4 class="card-title">Edit : {{form.vars.value.title}}</h4>
						<br>
						{{form_start(form)}}
						<div class="col-md-4">
							<div class="fileinput fileinput-new text-center" style="width:100%"  data-provides="fileinput">
								<div class="fileinput-new thumbnail" style="width:100%" >
									<a href="#" class="btn btn-rose btn-round btn-select"><i class="material-icons">image</i> Select image </a>
									<img  id="img-preview" src="{{asset(form.vars.value.poster.link)|imagine_filter('poster_thumb')}}"  width="100%">
								</div>
								{{form_widget(form.fileposter,{"attr":{"class":"file-hidden input-file img-selector","style":"    display: none;"}})}}
								<span class="validate-input">{{form_errors(form.fileposter)}}</span>
							</div>
						</div>
						<div class="col-md-8">
							<div class="fileinput fileinput-new text-center"  data-provides="fileinput">
								<div class="fileinput-new thumbnail" >
									<a href="#" class="btn btn-rose btn-round btn-select-1"><i class="material-icons">image</i> Select image </a>
									{% if form.vars.value.cover != null %}
										<img  id="img-preview-1" src="{{asset(form.vars.value.cover.link)|imagine_filter('cover_thumb')}}"  width="100%">
									{% else %}
										<img  id="img-preview-1" src="{{asset("img/image_placeholder.jpg")|imagine_filter('cover_thumb')}}"  width="100%">
									{% endif %}
								</div>
								{{form_widget(form.filecover,{"attr":{"class":"file-hidden input-file-1 img-selector-1","style":"    display: none;"}})}}
								<span class="validate-input">{{form_errors(form.filecover)}}</span>
							</div>
							<div class="form-group label-floating" style="float: right;">
								<label for="movie_titleEnglish" class="control-label" style="float: left;width: 100px;"><b>Enlgish title:</b></label>
								{{form_widget(form.titleEnglish,{"attr":{"class":"form-control", "style": "float: left;width: 200px;"}})}} 
							</div>
							<div class="form-group label-floating is-empty" style="display: inline-block;width: 100%;margin-top: -20px;">
								<label class="control-label">Movie title</label>
								<br>
								{{form_widget(form.title,{"attr":{"class":"form-control"}})}} 
								<span class="validate-input">{{form_errors(form.title)}}</span>
							</div>
							<div class="form-group label-floating is-empty">
								<label class="control-label">Movie description</label>
								{{form_widget(form.description,{"attr":{"class":"form-control","rows":8}})}}
								<span class="validate-input">{{form_errors(form.description)}}</span>
							</div>
							<div class="form-group label-floating is-empty">
								<label class="control-label">Movie year</label>
								{{form_widget(form.year,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.year)}}</span>
							</div>
							<div class="form-group label-floating is-empty">
								<label class="control-label">Movie classification</label>
								{{form_widget(form.classification,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.classification)}}</span>
							</div>
							<div class="form-group label-floating is-empty">
								<label class="control-label">Movie IMdb Rating</label>
								{{form_widget(form.imdb,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.imdb)}}</span>
							</div>
							<div class="form-group label-floating is-empty">
								<label class="control-label">Movie duration</label>
								{{form_widget(form.duration,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.duration)}}</span>
							</div>
							<div class="form-group label-floating is-empty" {% if not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin %}style="display:none"{% endif %}>
								<label class="control-label">Play Movie :  </label>
								{{form_widget(form.playas,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.playas)}}</span>
							</div>
							<div class="form-group label-floating is-empty" {% if not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin %}style="display:none"{% endif %}>
								<label class="control-label">Download Movie :  </label>
								{{form_widget(form.downloadas,{"attr":{"class":"form-control"}})}}
								<span class="validate-input">{{form_errors(form.downloadas)}}</span>
							</div>
							<div class="form-group label-floating ">
								<label class="control-label">Movies tags (Ex:anim,art,hero)</label>
								<br>
								{{form_widget(form.tags,{"attr":{"class":"input-tags"}})}}
								<span class="validate-input">{{form_errors(form.tags)}}</span>
							</div>
							<div class="" {% if not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin %}style="display:none"{% endif %}>
								<label>
									{{form_widget(form.enabled)}}  Enabled
								</label>
							</div>
							<div class="" {% if not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin %}style="display:none"{% endif %}>
								<label>
									{{form_widget(form.comment)}}  Enabled Comment
								</label>
							</div>
							<div class="">
								<label>
									{{form_widget(form.first)}} First for 3 days
								</label>
							</div>
							<br>
							{{form_label(form.genres,null,{label_attr:{"style":"font-size:16px"}})}} :
							<div>
								<div class="row">
									{% for field in form.genres %}
										<label  style="background: #000;border-radius: 20px;padding: 5px;text-align: center;margin: 10px;color: white;font-weight: bold;padding-left: 20px;padding-right: 20px;">
											{{ form_widget(field) }} {{ field.vars.label }}
										</label>
									{% endfor %}
								</div>
							</div>
							<script>
								$('.input-tags').selectize({
								persist: false,
								createOnBlur: true,
								create: true
								});
							</script>
						</div>
						<div class="col-md-12">
							<hr>
							<span class="pull-right"><a href="{{path("app_movie_index")}}" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
						</div>
						{{form_end(form)}}
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock%}