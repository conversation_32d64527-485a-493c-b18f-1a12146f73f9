{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card card-stats" style="margin: 15px 0;">
					<div class="card-content views-body  pull-right">
						<a href="#" class="btn btn-tab-movie"><i class="material-icons">remove_red_eye</i> {{movie.viewsnumber}} Views</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">share</i> {{movie.sharesnumber}} Shares</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">cloud_download</i> {{movie.downloadsnumber}} Downloads</a>
					</div>
					<div class="card-header " data-background-color="green">
						<i class="material-icons">movie_filter</i>
					</div>
					<div class="card-content trailer-body">
						<h4 class="title">Edit {{movie.title }} </b></h4>
						<div class="tab-moivie">
							<a href="{{path("app_movie_edit",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">edit</i> Edit</a>
							<a href="{{path("app_movie_sources",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">folder</i> Sources</a>
							<a href="{{path("app_movie_cast",{"id":movie.id})}}" class="btn btn-tab-movie-active"><i class="material-icons">recent_actors</i> Cast</a>
							<a href="{{path("app_movie_trailer",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">movie_filter</i> Trailer</a>
							<a href="{{path("app_movie_subtitles",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">subtitles</i> Subtitles</a>
							<a href="{{path("app_movie_comments",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">comment</i> Comments</a>
							<a href="{{path("app_movie_ratings",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">star_half</i> Ratings</a>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12">
				<div class="card">
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">recent_actors</i>
					</div>
					<div class="card-content">
						<h4 class="card-title">{{movie.title}} Cast</h4>
					</div>
				</div>
				<div class="row">
					<div class="col-md-3">
						<div class="card card-profile">
							<div class="card-avatar">
								<a href="#">
									<img class="img" src="{{asset("img/add.png")|imagine_filter('actor_thumb')}}">
								</a>
							</div>
							<div class="card-content">
								{{form_start(role_form)}}
								<div class="form-group label-floating is-empty">
									{{form_widget(role_form.role,{"attr":{"class":"form-control form-control-role","placeholder":"Role"}})}}
									<span class="validate-input">{{form_errors(role_form.role)}}</span>
								</div>
								<div class="form-group label-floating is-empty">
									{{form_widget(role_form.actor,{"attr":{"class":"form-control form-control-role","placeholder":"Actor/Director"}})}}
									<span class="validate-input">{{form_errors(role_form.actor)}}</span>
								</div>
								<span>{{form_widget(role_form.save,{attr:{"class":"btn btn-rose btn-xs form-control-role"}})}}</span>
								{{form_end(role_form)}}
							</div>
						</div>
					</div>
					<script type="text/javascript">
					$("#Role_actor").selectize();
					</script>
					{% for role in movie.roles %}
						<div class="col-md-3">
							<div class="card card-profile">
								<div class="card-avatar">
									<a href="#">
										<img class="img" src="{{asset(role.actor.media.link)|imagine_filter('actor_thumb')}}">
									</a>
								</div>
								<div class="card-content">
									<h5 class="card-title"><b>{{role.actor.name}}</b></h5>
									<h5 class="card-sub-title" style="color:gray"><b>{{role.role}}</b></h5>
									<a href="{{path("app_role_edit",{"id":role.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">edit</i></a>
									<a href="{{path("app_role_delete",{"id":role.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">delete</i></a>
									<a href="{{path("app_role_up",{"id":role.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">keyboard_arrow_up</i></a>
									<a href="{{path("app_role_down",{"id":role.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">keyboard_arrow_down</i></a>
								</div>
							</div>
						</div>
					{% endfor %}
				</div>
			</div>
		</div>
	</div>
{% endblock%}