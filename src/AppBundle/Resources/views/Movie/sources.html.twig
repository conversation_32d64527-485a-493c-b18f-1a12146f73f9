{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card card-stats" style="margin: 15px 0;">
					<div class="card-content views-body  pull-right">
						<a href="#" class="btn btn-tab-movie"><i class="material-icons">remove_red_eye</i> {{movie.viewsnumber}} Views</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">share</i> {{movie.sharesnumber}} Shares</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">cloud_download</i> {{movie.downloadsnumber}} Downloads</a>
					</div>
					<div class="card-header " data-background-color="green">
						<i class="material-icons">movie_filter</i>
					</div>
					<div class="card-content trailer-body">
						<h4 class="title">Edit {{movie.title }} </b></h4>
						<div class="tab-moivie">
							<a href="{{path("app_movie_edit",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">edit</i> Edit</a>
							<a href="{{path("app_movie_sources",{"id":movie.id})}}" class="btn btn-tab-movie-active"><i class="material-icons">folder</i> Sources</a>
							<a href="{{path("app_movie_cast",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">recent_actors</i> Cast</a>
							<a href="{{path("app_movie_trailer",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">movie_filter</i> Trailer</a>
							<a href="{{path("app_movie_subtitles",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">subtitles</i> Subtitles</a>
							<a href="{{path("app_movie_comments",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">comment</i> Comments</a>
							<a href="{{path("app_movie_ratings",{"id":movie.id})}}" class="btn btn-tab-movie"><i class="material-icons">star_half</i> Ratings</a>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12">
				<div class="card">
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">folder</i>
					</div>
					<a  href="{{path("app_source_add",{"poster":movie.id})}}" class="btn  btn-xs pull-right new-subtitle-source"><i class="material-icons" style="font-size: 30px;">add_box</i> New Source</a>
					<div class="card-content">
						<h4 class="card-title">{{movie.title}} Sources</h4>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<div class="card" style="margin: 15px 0;">
							<div class="table-responsive">
								<table class="table table-source" width="100%">
									<thead class="text-primary">
										<tr>
											<th><b>Type</b></th>
											<th><b>Url</b></th>
											<th style="text-align:right"><b>Action</b></th>
										</tr>
									</thead>
									<tbody>
										{% for source in movie.sources %}
											<tr>
												<td><b>{{source.type|capitalize}}  {% if source.type == "file" and source.media != null %}({{source.media.extension|capitalize}}){% endif %} </b></td>
												<td><span class="label label-info" style="    text-transform: inherit;">{{source.url}} {% if source.type == "file" and source.media != null %} {{ app.request.scheme ~'://' ~ app.request.httpHost ~ asset(source.media.link) }}{% endif %}</span></td>
												<td>
													<a href="{{path("app_source_edit",{"id":source.id})}}" class="btn btn-rose btn-xs pull-right" ><i class="material-icons">edit</i></a>
													<a href="{{path("app_source_delete",{"id":source.id})}}" class="btn btn-rose btn-xs pull-right"><i class="material-icons">delete</i></a>
												</td>
											</tr>
										{% else %}
											<tr>
												<td rowspan="2">
													<br>
													<center><img src="{{asset("img/bg_empty.png")}}"  style="width: auto !important;" =""></center>
													<br>
												</td>
											</tr>
										{% endfor %}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock%}