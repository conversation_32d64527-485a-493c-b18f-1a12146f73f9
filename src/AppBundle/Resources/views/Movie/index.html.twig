{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-4">
						<a href="{{path("app_movie_index")}}" class="btn  btn-lg btn-warning col-md-12"><i class="material-icons" style="font-size: 30px;">refresh</i> Refresh</a>
					</div>
<div class="col-md-4">
    <a class="btn btn btn-lg btn-yellow col-md-12">
        <i class="material-icons" style="font-size: 30px;">movie</i>
        {% if selected_genre and genre_counts[selected_genre] is defined %}
            {{ genre_counts[selected_genre] }} Movies in Selected Genre
        {% else %}
            {{ movies.totalItemCount }} Movies
        {% endif %}
    </a>
</div>


					<div class="col-md-4">
						<a href="{{path("app_movie_add")}}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title=""><i class="material-icons" style="font-size: 30px;">add_box</i> NEW MOVIE </a>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<form>
							<div class="row">
								<div class="col-md-4">
									<input name="q" value="{{app.request.query.get("q")}}" type="text" class="search-input" placeholder="Search by title">
								</div>
								<div class="col-md-4">
<select name="genre" class="form-control">
    <option value="">All Genres ({{ movies.totalItemCount }})</option>
    {% for genre in genres %}
        <option value="{{ genre.id }}" {% if app.request.get('genre') == genre.id %}selected{% endif %}>
            {{ genre.title }} ({{ genre_counts[genre.id] }})
        </option>
    {% endfor %}
</select>



								</div>

								<div class="col-md-4">
									<button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i></button>
								</div>
							</div>
							     <!-- שורת הסינון החדשה לפי טווחי זמן -->
            <div class="row" style="margin-top: 20px;">
                <div class="col-md-12">
                    <form method="get" action="{{ path('app_movie_index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="start_date">מתאריך</label>
                                <input type="date" id="start_date" name="start_date" class="form-control" value="{{ app.request.query.get('start_date') }}">
                            </div>
                            <div class="col-md-3">
                                <label for="end_date">עד תאריך</label>
                                <input type="date" id="end_date" name="end_date" class="form-control" value="{{ app.request.query.get('end_date') }}">
                            </div>
                            
                            <div class="col-md-6 text-right">
                                <br>
                                <button type="submit" class="btn btn-secondary">סנן לפי תאריכים</button>
                                <button type="submit" name="filter" value="last_30_days" class="btn btn-secondary">30 יום אחרונים</button>
                                <button type="submit" name="filter" value="last_week" class="btn btn-secondary">שבוע אחרון</button>
                                <button type="submit" name="filter" value="today" class="btn btn-secondary">היום</button>
                                <button type="submit" name="filter" value="this_year" class="btn btn-secondary">השנה</button>
                                <button type="submit" name="filter" value="last_year" class="btn btn-secondary">שנה שעברה</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
						</form>

					</div>
				</div>
				<div class="row">
					{% for movie in movies %}
						<div class="col-md-4 col-sm-6 col-lg-3 col-xl-2">
							<div class="card">
								<div class="card-content" style="text-align: center; padding: 0px;">
									<img src="{{ asset(movie.poster.link)|imagine_filter('poster_thumb') }}" >
									<br>
									<span class="label-lang">{{ movie.title }}</span>
								</div>
								<div class="card-footer" style="text-align: center;">
									<a href="{{ path("app_movie_edit", {"id": movie.id}) }}" rel="tooltip" data-placement="left" class="btn btn-primary btn-xs btn-round" data-original-title="Edit">
										<i class="material-icons">edit</i>
									</a>
									{% if movie.cover != null %}
										<a href="{{ path("app_home_notif_poster", {title: movie.title, id: movie.id, image: asset(movie.cover.link)|imagine_filter("cover_thumb"), icon: asset(movie.poster.link)|imagine_filter("poster_thumb")}) }}" rel="tooltip" data-placement="left" class="btn btn-primary btn-xs btn-round" data-original-title="Notification">
											<i class="material-icons">notifications</i>
										</a>
									{% else %}
										<a href="{{ path("app_home_notif_poster", {title: movie.title, id: movie.id, image: asset(movie.poster.link)|imagine_filter("cover_thumb"), icon: asset(movie.poster.link)|imagine_filter("poster_thumb")}) }}" rel="tooltip" data-placement="left" class="btn btn-primary btn-xs btn-round" data-original-title="Notification">
											<i class="material-icons">notifications</i>
										</a>
									{% endif %}
									<a href="{{ path("app_movie_delete", {"id": movie.id}) }}" rel="tooltip" data-placement="left" class="btn btn-danger btn-xs btn-round" data-original-title="Delete">
										<i class="material-icons">delete</i>
									</a>
									<!-- כמות צפיות מתחת לכפתורים -->
    <div class="views-count">
        <span>Views: {{ movie.views }}</span>
    </div>
								</div>
							</div>
						</div>
					{% else %}
						<div class="col-md-12">
							<div class="card">
								<div class="card-content">
									<br>
									<br>
									<center><img src="{{ asset("img/bg_empty.png") }}" style="width: auto !important;"></center>
									<br>
									<br>
								</div>
							</div>
						</div>
					{% endfor %}
				</div>
				<div class="pull-right">
					{{ knp_pagination_render(movies) }}
				</div>
			</div>
		</div>
	{% endblock %}
