<?php 

$posters =  array();
$yearlyPosters = [];
$imdbPosters = [];
foreach ($posters_list as $key => $poster) {
	$pstr = null;
	$pstr["id"]= $poster->getId();
	$pstr["type"]= $poster->getType();
	$pstr["title"]= $poster->getTitle();
	$pstr["description"]= $poster->getDescription();
	$pstr["year"]= $poster->getYear();
	$pstr["imdb"]= $poster->getImdb();
	$pstr["isNew"]= false;
	$pstr["comment"]= $poster->getComment();
	$pstr["rating"]= $poster->getRating();
	$pstr["duration"] = $poster->getDuration();
	$pstr["downloadas"] = $poster->getDownloadas();
	$pstr["playas"] = $poster->getPlayas();
	$pstr["classification"]= $poster->getClassification();
	$pstr["image"] = $this['imagine']->filter($view['assets']->getUrl($poster->getPoster()->getLink()), 'poster_thumb');
	if($poster->getCover())
		$pstr["cover"] = $this['imagine']->filter($view['assets']->getUrl($poster->getCover()->getLink()), 'cover_thumb');


	$genre_poster_list =  array();
	foreach ($poster->getGenres() as $key => $genre_poster) {
		$genre_poster_obj = array();
		$genre_poster_obj["id"]=$genre_poster->getId();
		$genre_poster_obj["title"]=$genre_poster->getTitle();
		$genre_poster_list[] = $genre_poster_obj;
	}
	$pstr["genres"] = $genre_poster_list;

	if($poster->getTrailer()){
		$trailer_poster_obj["id"]=$poster->getTrailer()->getId();
		if ($poster->getTrailer()->getType()=="file") {
			$trailer_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $poster->getTrailer()->getMedia()->getLink();
			$trailer_poster_obj["type"]=$poster->getTrailer()->getMedia()->getExtension();

		}else{
			$trailer_poster_obj["type"]=$poster->getTrailer()->getType();
			$trailer_poster_obj["url"]=$poster->getTrailer()->getUrl();
		}
		$pstr["trailer"] = $trailer_poster_obj;
	}
	$source_poster_list =  array();
	foreach ($poster->getSources() as $key => $source_poster) {
		$source_poster_obj = array();
		$source_poster_obj["id"]=$source_poster->getId();
		if ($source_poster->getType()=="file") {
			$source_poster_obj["url"]=$app->getRequest()->getScheme()."://".$app->getRequest()->getHttpHost()."/". $source_poster->getMedia()->getLink();
			$source_poster_obj["type"]=$source_poster->getMedia()->getExtension();

		}else{
			$source_poster_obj["type"]=$source_poster->getType();
			$source_poster_obj["url"]=$source_poster->getUrl();
		}
		$source_poster_list[] = $source_poster_obj;
	}
	$pstr["sources"] = $source_poster_list;

	$posters[]=$pstr;
	// Group posters by year dynamically
    $yearlyPosters[$poster->getYear()][] = $pstr;
    $imdbPosters[(string)$poster->getImdb()][] = $pstr;

}
if(in_array($order, ['year'])){
	foreach ($yearlyPosters as &$posters) {
		shuffle($posters); // Shuffle posters for each year
	}

	$shuffledPosters = [];
	foreach ($yearlyPosters as $yearPosters) {
		$shuffledPosters = array_merge($shuffledPosters, $yearPosters);
	}
	$posters = $shuffledPosters;
}
if(in_array($order, ['imdb'])){

    // Shuffle posters within the same IMDb rating group
    foreach ($imdbPosters as &$posters) {
        shuffle($posters);
    }

    // Sort by IMDb rating (as float)
    krsort($imdbPosters, SORT_NUMERIC); // Sort by keys (IMDb ratings) in descending order

    // Combine shuffled groups into a single array
    $shuffledPosters = [];
    foreach ($imdbPosters as $ratingPosters) {
        $shuffledPosters = array_merge($shuffledPosters, $ratingPosters);
    }

    // Final posters array
    $posters = $shuffledPosters;
}
if(!in_array($order, ['year', 'imdb'])){
	shuffle($posters);
}
echo json_encode($posters, JSON_UNESCAPED_UNICODE);
?>