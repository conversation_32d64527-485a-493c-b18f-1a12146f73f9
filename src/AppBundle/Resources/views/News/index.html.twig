{% extends "AppBundle::layout.html.twig" %}
{% block body%}

	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-4">
						<a href="{{path("app_home_notif_news_index")}}" class="btn  btn-lg btn-warning col-md-12"><i class="material-icons" style="font-size: 30px;">refresh</i> Refresh</a>
					</div>
					<div class="col-md-4">
					</div>
					<div class="col-md-4">
						<a href="{{path("app_home_notif_news_new")}}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title=""><i class="material-icons" style="font-size: 30px;">add_box</i> NEW news </a>
					</div>
				</div>
				<div class="card">
					<div class="card-content">
						<h4 class="card-title">News list</h4>
						<div class="table-responsive">
							<table class="table" width="100%">
								<thead class="text-primary">
									<tr>
										<th width="70px">#</th>
										<th>News</th>
										<th>State</th>
										<th width="160px">Action</th>
									</tr>
								</thead>
								<tbody>
									{% for news in pagination %}
										<tr>
											<td width="70px">
												{{news.id}}
											</td>
											<td>{{news.news}}</td>
											<td>
												{% if news.active %}
													<span class="label label-success">Active</span>
												{% else %}
													<span class="label label-danger">Inactive</span>
												{% endif %}
											</td>
											<td>
												<a href="{{path("app_home_notif_news_edit",{"id":news.id})}}" rel="tooltip" data-placement="left" class=" btn btn-primary btn-xs btn-round" data-original-title="View">
													<i class="material-icons">edit</i>
												</a>
												<a href="{{path("app_home_notif_news_delete",{"id":news.id})}}" rel="tooltip" data-placement="left" class=" btn btn-primary btn-xs btn-round" data-original-title="Delete">
													<i class="material-icons">delete</i>
												</a>
											</td>
										</tr>
									{% else %}
										<tr>
											<td colspan="3">
												<br>
												<br>
												<center><img src="{{asset("img/bg_empty.png")}}"  style="width: auto !important;"></center>
												<br>
												<br>
											</td>
										</tr>
									{% endfor %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div class=" pull-right">
					{{ knp_pagination_render(pagination) }}
				</div>
			</div>
		</div>
		
{% endblock%}
