{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<style>
	.main-panel > .content {
		    margin-top: 35px;
	}
	.modal-backdrop { z-index: -999999; }
	/* Full-window loader styles */
	#loader-wrapper {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.8); /* Background color with transparency */
		z-index: 9999; /* Ensure it's above all other content */
		display: flex;
		align-items: center;
		justify-content: center;
		
	}

	.loader {
		border: 10px solid #ccc; /* Light grey */
		border-top: 10px solid #3498db; /* Blue */
		border-bottom: 10px solid #3498db; /* Blue */
		padding:10px;
		background-color: #fff;
		font-weight: bold;
	}

	{# @keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	} #}
	</style>
	
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card card-stats" style="margin: 15px 0;">
					<div class="card-content views-body  pull-right">
						<a href="#" class="btn btn-tab-movie"><i class="material-icons">remove_red_eye</i> {{serie.viewscountnumber}} Views</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">share</i> {{serie.sharesnumber}} Shares</a>
						<a href="#" class="btn btn-tab-movie pull-right"><i class="material-icons">cloud_download</i> {{serie.downloadscountnumber}} Downloads</a>
					</div>
					<div class="card-header " data-background-color="green">
						<i class="material-icons">tv</i>
					</div>
					<div class="card-content trailer-body">
						<h4 class="title">Edit {{serie.title }} </b></h4>
						<div class="tab-moivie">
							<a href="{{path("app_serie_edit",{"id":serie.id})}}" class="btn btn-tab-movie"><i class="material-icons">edit</i> Edit</a>
							<a href="{{path("app_serie_cast",{"id":serie.id})}}" class="btn btn-tab-movie"><i class="material-icons">recent_actors</i> Cast</a>
							<a href="{{path("app_serie_trailer",{"id":serie.id})}}" class="btn btn-tab-movie"><i class="material-icons">movie_filter</i> Trailer</a>
							<a href="{{path("app_serie_seasons",{"id":serie.id})}}" class="btn btn-tab-movie-active"><i class="material-icons">dynamic_feed</i> SEASONS</a>
							<a href="{{path("app_serie_comments",{"id":serie.id})}}" class="btn btn-tab-movie"><i class="material-icons">comment</i> Comments</a>
							<a href="{{path("app_serie_ratings",{"id":serie.id})}}" class="btn btn-tab-movie"><i class="material-icons">star_half</i> Ratings</a>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-12">
				<div class="card">
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">dynamic_feed</i>
					</div>
					<a  href="#" class="btn  btn-xs btn-success pull-right new-subtitle-source" id="new_season_btn" style="    position: absolute;"><i class="material-icons" style="font-size: 30px;">add_box</i> New Season</a>
					<a  href="#" class="btn  btn-xs btn-success pull-right new-subtitle-source btn-auto-add-season" id="btn-auto-add-season" style="position: absolute; margin-right: 135px;"><i class="material-icons" style="font-size: 30px;">add_box</i> Auto add/update Season</a>
					<a  href="{{path('app_get_from_api', {'id': serie.id})}}" class="btn  btn-xs btn-success pull-right new-subtitle-source btn-fetch-from-api" style="position: absolute; margin-right: 340px;"><i class="material-icons" style="font-size: 30px;">download</i> Take From API</a>
					<div class="card-content">
						<h4 class="card-title">{{serie.title}} Seasons</h4>
					</div>
				</div>
			</div>
			<div id="loader-wrapper" style="display:none">
				<div class="loader">We are processing your request, please wait....</div>
			</div>
			<div class="col-md-12" id="new_season_dialog" style="display:none">
				<div class="card">
					{{form_start(season_form)}}
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">add</i>
					</div>
					<div class="card-content">
						<h4 class="card-title">Add new season</h4>
						<br>
						<div class="form-group label-floating is-empty">
							<label class="control-label">Season title</label>
							{{form_widget(season_form.title,{"attr":{"class":"form-control"}})}}
							<span class="validate-input">{{form_errors(season_form.title)}}</span>
						</div>
						<span class="pull-right"><a href="#" id="new_season_btn_close" class="btn btn-fill btn-info"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(season_form.save,{attr:{"class":"btn btn-fill btn-success"}})}}</span>
					</div>
					{{form_end(season_form)}}
				</div>
			</div>
			{% for season in serie.seasons %}
				<div class="col-md-12 season-list">
					<div class="card">
						<a href="#" class="btn btn-tab-season"><i class="material-icons">dynamic_feed</i> {{season.title}}</a>
						<a href="{{path("app_season_edit",{"id":season.id})}}" class="btn  btn-xs btn-tab-season-action pull-right"><i class="material-icons">edit</i></a>
						<a href="{{path("app_season_delete",{"id":season.id})}}" class="btn  btn-xs btn-tab-season-action pull-right"><i class="material-icons">delete</i></a>
						<a href="{{path("app_season_up",{"id":season.id})}}" class="btn  btn-xs btn-tab-season-action pull-right"><i class="material-icons">keyboard_arrow_up</i></a>
						<a href="{{path("app_season_down",{"id":season.id})}}" class="btn  btn-xs btn-tab-season-action pull-right"><i class="material-icons">keyboard_arrow_down</i></a>
						<a href="{{path("app_episode_add",{"id":season.id})}}" class="btn  btn-xs btn-tab-season-action pull-right"><i class="material-icons">add</i>New Episode</a>
						<a href="#" class="btn  btn-xs btn-tab-season-action pull-right btn_auto_episode" id="{{season.id}}" title=""><i class="material-icons">add</i>Auto Episodes</a>
						<br>
						<div class="episodes-contrainer ">
							<div class="episodes-list">
								{% for episode in season.episodes %}
									<div class="card card-episode episode-list">
										{% if episode.media == null %}
											<img class="img" src="{{asset(season.poster.poster.link)|imagine_filter('actor_thumb')}}">
										{% else %}
											<img class="img" src="{{asset(episode.media.link)|imagine_filter('episode_thumb')}}">
										{% endif %}
										<div>
											<h4>{{episode.title}}</h4>
											<p>{{episode.description}}</p>
											<div>
												<a href="{{path("app_episode_edit",{"id":episode.id})}}" class="btn  btn-xs btn-success pull-right"><i class="material-icons">edit</i></a>
												<a href="{{path("app_episode_delete",{"id":episode.id})}}" class="btn  btn-xs btn-success pull-right"><i class="material-icons">delete</i></a>
												<a href="{{path("app_episode_down",{"id":episode.id})}}" class="btn  btn-xs btn-success pull-right"><i class="material-icons">keyboard_arrow_down</i></a>
												<a href="{{path("app_episode_up",{"id":episode.id})}}" class="btn  btn-xs btn-success pull-right"><i class="material-icons">keyboard_arrow_up</i></a>
											</div>
										</div>
									</div>
								{% endfor %}
							</div>
						</div>
					</div>
				</div>
			{% endfor %}
		</div>
	</div>
		
	<!-- Modal -->
	<div class="modal fade" id="addEpisode" tabindex="-1" role="dialog" aria-labelledby="modelTitleId" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Add Auto Episode</h5>
						{# <button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button> #}
				</div>
				<form id="formAddEpisodes">
					<div class="modal-body">
						<input type="hidden" id="season_id" />
						<label>Number of Episode</label>
						<input type="number" class="form-control" id="number_of_episodes" placeholder="Numbers of Episodes" required />
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
						<button type="submit" class="btn btn-primary">Submit</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- add Modal -->
	<div class="modal fade" id="autoAddSeason" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel">Auto add/update season</h4>
			</div>
			<div class="modal-header">
				<div style="display: inline-block;"><p class="modal-title" id="myModalLabel">Add Season</p></div>
				<div style="display: inline-block;"><input type="number" style="width: 50px; height: 30px;" class="season-count"></div>
				<div style="display: inline-block;"><button type="button" class="btn btn-sm btn-danger add-all-season">Add</button></div>
			</div>
			<form id="addForm" method="post" action="/serie/add-update/seasons">
				<input type="hidden" name="id" value="{{serie.id}}">
				<div class="modal-body">

				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
					<button type="submit" class="btn btn-primary">Save</button>
				</div>
			</form>
			</div>
		</div>
	</div>

	<script>
		
		$(document).ready(function(){
const protocol = window.location.protocol; // 'http:' or 'https:'
const host = window.location.host; // e.g., 'supadmin.stagenachos.online'
const $base_url = `${protocol}//${host}/`;
			var htmlStr = '<div class="row"><div class="col-md-1"></div><div class="col-md-2"><p class="modal-title" style="margin-top: 5px; font-weight: bold;" id="myModalLabel">Season __sno__</p></div><div class="col-md-9"><input name="season[__sno__][ep]" type="number" style="width: 50px; height: 30px; display: inline-block;" class="season-count" value="__epcount__" /><p style="display: inline-block;">&nbsp;&nbsp;episodes</p><p></p></div></div>';
			$('.btn_auto_episode').click(function(e){
				e.preventDefault();
				//alert('hello');
				var season = $(this).attr('id');
				$('#season_id').val(season);
				$('#addEpisode').modal('show');
			})
			$('.btn-auto-add-season').click(function(e){
				e.preventDefault();
				$('#autoAddSeason').find('.modal-body').html('');
				$('.season-count').val($('.season-list').length);
				var count = 1;
				$('.season-list').each(function(){
					var episodeLength = $(this).find('.episode-list').length;
					var serieData = htmlStr.replace(/__sno__/gi, count);
					var serieData = serieData.replace(/__epcount__/gi, episodeLength);
					$('#autoAddSeason').find('.modal-body').append(serieData);
					count = count + 1;
				});
				$('#autoAddSeason').modal('show');
			});
			$('.add-all-season').click(function(e){
				$('#autoAddSeason').find('.modal-body').html('');
				var count = 1;
				var totalSeason = $('.season-count').val();
				var existingSeasons = [];
				$('.season-list').each(function(){
					var episodeLength = $(this).find('.episode-list').length;
					existingSeasons[count] = episodeLength;
					count = count + 1;
				});
				for (let i = 1; i <= totalSeason; i++) {
					var episodeLength = existingSeasons[i] ? existingSeasons[i] : 0;
					var serieData = htmlStr.replace(/__sno__/gi, i);
					var serieData = serieData.replace(/__epcount__/gi, episodeLength);
					$('#autoAddSeason').find('.modal-body').append(serieData);
				}
			});
			
			$('#formAddEpisodes').submit(function(e){
				e.preventDefault();
				var data = new FormData();
				data.append('season_id',$('#season_id').val());
				data.append('total_episode',$('#number_of_episodes').val());
				 $.ajax({
							type: "POST",
							data: data,
							url: $base_url+"api/episode/add/",
							mimeType: "multipart/form-data",
							contentType: false,
							cache: false,
							processData: false,
							success: function(data) {
								var $data = jQuery.parseJSON(data);
								/*
								$('#complaint_id').empty().append($data.complaint);
								$('#statement_id').empty().append($data.driver_statement);
								$('#sign_prev').attr( 'src' ,$data.signature);
								$('#sign_prev').css("display", "block");;

								$('#hidden_complaint_id').val($data.id);
								// alertify.success('submitted');
								// refresh_table(0);
								*/
								$('#addEpisode').modal('hide');
								alert('added');
								//location.reload();
								window.location.href = $base_url+"serie/seasons/"+"{{serie.id}}.html";
								return;
							},
							error: function(data) {
								//alertify.error('Something Happened');

							}
						});
			});
			{% if inProgress %}
				$('#loader-wrapper').show();
				$('.main-panel').css('overflow', 'hidden');
			{% endif %}
		});
		
	</script>
{% endblock%}