{% extends "AppBundle::layout.html.twig" %}
{% block body %}
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ path('app_serie_index') }}" class="btn btn-lg btn-warning col-md-12">
                            <i class="material-icons" style="font-size: 30px;">refresh</i> Refresh
                        </a>
                    </div>
<div class="col-md-4">
    <a class="btn btn btn-lg btn-yellow col-md-12">
        <i class="material-icons" style="font-size: 30px;">tv</i>
        {% if selected_genre and genre_counts[selected_genre] is defined %}
            {{ genre_counts[selected_genre] }} Series in Selected Genre
        {% else %}
            {{ series.totalItemCount }} Series TV
        {% endif %}
    </a>
</div>


                    <div class="col-md-4">
                        <a href="{{ path('app_serie_add') }}" class="btn btn-rose btn-lg pull-right add-button col-md-12">
                            <i class="material-icons" style="font-size: 30px;">add_box</i> NEW SERIE TV
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <form method="get" action="{{ path('app_serie_index') }}">
                            <div class="row">
                                <div class="col-md-4">
                                    <input name="q" value="{{ app.request.query.get('q') }}" type="text" class="search-input" placeholder="Search by title">
                                </div>
                                <div class="col-md-4">
                                  <select name="genre" class="form-control">
    <option value="">All Genres ({{ total_series_count }})</option> <!-- הצגת מספר הסדרות הכולל כאן -->
    {% for genre in genres %}
        <option value="{{ genre.id }}" {% if app.request.get('genre') == genre.id %}selected{% endif %}>
            {{ genre.title }} ({{ genre_counts[genre.id]|default(0) }})
        </option>
    {% endfor %}
</select>

                                </div>
                                <div class="col-md-4 text-right">
                                    <button type="submit" class="btn btn-sm search-btn">
                                        <i class="material-icons" style="font-size: 30px;">search</i>
                                    </button>
                                </div>
                            </div>

                            <!-- שורת הסינון לפי טווחי זמן ומיון -->
                            <div class="row" style="margin-top: 20px;">
                                <div class="col-md-3">
                                    <label for="start_date">מתאריך</label>
                                    <input type="date" id="start_date" name="start_date" class="form-control" value="{{ app.request.query.get('start_date') }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="end_date">עד תאריך</label>
                                    <input type="date" id="end_date" name="end_date" class="form-control" value="{{ app.request.query.get('end_date') }}">
                                </div>
                                <div class="col-md-3">
                                    <label for="filter">סינון לפי תקופה</label>
                                    <select name="filter" class="form-control">
                                        <option value="">All Time</option>
                                        <option value="last_30_days" {% if app.request.query.get('filter') == 'last_30_days' %}selected{% endif %}>Last 30 Days</option>
                                        <option value="last_week" {% if app.request.query.get('filter') == 'last_week' %}selected{% endif %}>Last Week</option>
                                        <option value="today" {% if app.request.query.get('filter') == 'today' %}selected{% endif %}>Today</option>
                                        <option value="this_year" {% if app.request.query.get('filter') == 'this_year' %}selected{% endif %}>This Year</option>
                                        <option value="last_year" {% if app.request.query.get('filter') == 'last_year' %}selected{% endif %}>Last Year</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="order">מיון לפי</label>
                                    <select name="order" class="form-control">
                                        <option value="created" {% if app.request.query.get('order') == 'created' %}selected{% endif %}>Newest</option>
                                        <option value="views" {% if app.request.query.get('order') == 'views' %}selected{% endif %}>Most Viewed</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row" style="margin-top: 20px;">
                    {% for serie in series %}
                        <div class="col-md-4 col-sm-6 col-lg-3 col-xl-2">
                            <div class="card">
                                <div class="card-content" style="text-align: center; padding: 0px;">
                                    <img src="{{ asset(serie.poster.link) | imagine_filter('poster_thumb') }}" alt="{{ serie.title }}">
                                    <br>
                                    <span class="label-lang">{{ serie.title }}</span>
                                </div>
                                <div class="card-footer" style="text-align: center;">
                                    {% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin or serie.user == app.user %}
                                        <a href="{{ path('app_serie_edit', {'id': serie.id}) }}" rel="tooltip" class="btn btn-primary btn-xs btn-round" data-original-title="Edit">
                                            <i class="material-icons">edit</i>
                                        </a>
                                    {% else %}
                                        <a href="{{ path('app_serie_seasons', {'id': serie.id}) }}" rel="tooltip" class="btn btn-primary btn-xs btn-round" data-original-title="Seasons">
                                            <i class="material-icons">edit</i>
                                        </a>
                                    {% endif %}

                                    {% if serie.cover %}
                                        <a href="{{ path('app_home_notif_poster', {'title': serie.title, 'id': serie.id, 'image': asset(serie.cover.link) | imagine_filter('cover_thumb'), 'icon': asset(serie.poster.link) | imagine_filter('poster_thumb')}) }}" rel="tooltip" class="btn btn-primary btn-xs btn-round" data-original-title="Notification">
                                            <i class="material-icons">notifications</i>
                                        </a>
                                    {% else %}
                                        <a href="{{ path('app_home_notif_poster', {'title': serie.title, 'id': serie.id, 'image': asset(serie.poster.link) | imagine_filter('cover_thumb'), 'icon': asset(serie.poster.link) | imagine_filter('poster_thumb')}) }}" rel="tooltip" class="btn btn-primary btn-xs btn-round" data-original-title="Notification">
                                            <i class="material-icons">notifications</i>
                                        </a>
                                    {% endif %}

                                    <a href="{{ path('app_serie_delete', {'id': serie.id}) }}" rel="tooltip" class="btn btn-danger btn-xs btn-round" data-original-title="Delete">
                                        <i class="material-icons">delete</i>
                                    </a>
                                </div>
                                <div class="views-count">
                                    <span>Views: {{ serie.views }}</span>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-content">
                                    <br><br>
                                    <center><img src="{{ asset('img/bg_empty.png') }}" style="width: auto !important;"></center>
                                    <br><br>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <div class="pull-right">
                    {{ knp_pagination_render(series) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
