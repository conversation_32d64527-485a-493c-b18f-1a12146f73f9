{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<script src="https://cdn.tiny.cloud/1/o6xid8u3x05byna9k1qy074m1lvnjcu1h450gdkpzc9kok13/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-2 col-md-2 col-sm-2">
        </div>
        <div class="col-lg-4 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">email</i>
                </div>
                <div class="card-content">
                    <p class="category"><b>Current State</b></p>
                    <h3 class="title">{{currentState['sentCount']??0}}/{{currentState['total']??0}}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">email</i>
                </div>
                <div class="card-content">
                    <p class="category"><b>Overall State</b></p>
                    <h3 class="title">{{overallState['sentCount']??0}}/{{overallState['total']??0}}</h3>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
       <div class="col-sm-offset-2 col-md-8">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">notifications_active</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Send emails</h4>
                    {{form_start(form)}}
                        <br>
                        <label class="control-label">Type</label>
                        <div class="form-group label-floating">
                            {{form_widget(form.senderType,{"attr":{"class":"form-control"}})}}
                        </div>
                        <div class="specific-users" style="display:none">
                            <label class="control-label">Select users</label>
                            <div class="form-group label-floating">
                                {{form_widget(form.emails,{"attr":{"class":"form-control"}})}}
                            </div>
                        </div>
                        <label class="control-label">Email subject</label>
                        <div class="form-group label-floating ">
                            {{form_widget(form.subject,{"attr":{"class":"form-control"}})}}
                        </div>

                        <label class="control-label">Template type</label>
                        <div class="form-group label-floating">
                            {{form_widget(form.emailTemplate,{"attr":{"class":"form-control"}})}}
                        </div>
                        <div class="specific-body" style="display:none">
                            <div class="form-group label-floating">
                                <label class="control-label">Email body</label>
                                {{form_widget(form.body,{"attr":{"class":"form-control"}})}}
                            </div>
                        </div>
                        <span class="pull-right">{{form_widget(form.send,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>

                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
      tinymce.init({
        selector: 'textarea#form_body',
        plugins: 'link image media table lists',
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | checklist numlist bullist indent outdent | emoticons charmap | removeformat'
    });
    var selector = $('#form_emails');
    selector.select2();
    $('#form_emailTemplate').on('change', function(){
        $('.specific-body').hide();
        if($(this).val() == 'custom'){
            $('.specific-body').show();
        }
    });
    $('#form_senderType').on('change', function(){
        selector.select2('destroy');
        selector.select2();
        $('.specific-users').hide();
        if($(this).val() == 'send_to_specific'){
            selector.select2('destroy');
            $('.specific-users').show();
            selector.select2({
                ajax: {
                    url: '/emails/get-users',
                    data: function (params) {
                        var query = {
                            search: params.term
                        }
                        return query;
                    },
                    processResults: function (data) {
                        return {
                            results: data
                        };
                    }
                    // Additional AJAX parameters go here; see the end of this chapter for the full code of this example
                }
            });
        }
    });
</script>
{% endblock%}