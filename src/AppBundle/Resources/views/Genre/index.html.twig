{% extends "AppBundle::layout.html.twig" %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-4">
            <a href="{{ path('app_genre_index') }}" class="btn btn-lg btn-warning col-md-12">
                <i class="material-icons" style="font-size: 30px;">refresh</i> Refresh
            </a>
        </div>
        <div class="col-md-4">
            <a class="btn btn-lg btn-yellow col-md-12">
                <i class="material-icons" style="font-size: 30px;">label</i> {{ genres|length }} genres
            </a>
        </div>
        <div class="col-md-4">
            <a href="{{ path('app_genre_add') }}" class="btn btn-rose btn-lg pull-right add-button col-md-12">
                <i class="material-icons" style="font-size: 30px;">add_box</i> NEW GENRE
            </a>
        </div>
    </div>

    <div class="row" style="margin-top: 20px;">
        <form id="genre-position-form">
            {% for genre in genres %}
                <div class="genre-row row" style="padding: 15px; margin-bottom: 10px; border-bottom: 1px solid #ccc; align-items: center;">
                    <div class="col-md-1">
                        <span style="font-size: 20px; font-weight: bold;">{{ genre.position }}</span>
                    </div>
                    <div class="col-md-3">
                        <span style="font-size: 18px;">{{ genre.title }}</span>
                    </div>
                    <div class="col-md-2">
                        <input class="js-genre-vip" data-id="{{ genre.id }}" type="checkbox"
                            {% if genre.forVip %}checked{% endif %}> VIP
                    </div>
                    <div class="col-md-2">
                        <select class="form-control genre-position-select" name="positions[{{ genre.id }}]">
                            {% for i in 1..genres|length %}
                                <option value="{{ i }}" {% if genre.position == i %}selected{% endif %}>{{ i }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ path('app_genre_edit', { 'id': genre.id }) }}" class="btn btn-xs btn-primary">
                            <i class="material-icons">edit</i>
                        </a>
                        <a href="{{ path('app_genre_delete', { 'id': genre.id }) }}" class="btn btn-xs btn-danger">
                            <i class="material-icons">delete</i>
                        </a>
                    </div>
                </div>
            {% endfor %}
        </form>
    </div>
</div>

<!-- סקריפט ה-VIP המקורי שלך נשמר -->
<script>
const protocol = window.location.protocol; // 'http:' or 'https:'
const host = window.location.host; // e.g., 'supadmin.stagenachos.online'
const $base_url = `${protocol}//${host}/`;
    $(document).ready(function() {
        $('body').on('change', '.js-genre-vip', function(e) {
            e.preventDefault();
            var id = $(this).attr('data-id');
            var status = $(this).prop('checked') ? 1 : 0;
            $.ajax({
                type: "GET",
                data: {status: status},
                url: $base_url + "genre/update-vip/" + id
            });
        });

        // סקריפט לעדכון המיקום
        document.querySelectorAll('.genre-position-select').forEach(select => {
            select.addEventListener('change', function() {
                const form = document.getElementById('genre-position-form');
                const formData = new FormData(form);

                fetch('{{ path('app_genre_update_position') }}', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'Position updated') {
                        window.location.reload(); // רענון הדף אחרי העדכון
                    }
                });
            });
        });
    });
</script>
{% endblock %}
