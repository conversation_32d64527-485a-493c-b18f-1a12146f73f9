{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<div class="container-fluid">
    <div class="row">
       <div class="col-md-12">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">recent_actors</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Edit :  {{form.vars.value.name}}</h4>
                    <br>
                    {{form_start(form)}}
                    <form method="#" action="#">
                        <div class="col-md-4">
                            <div class="fileinput fileinput-new text-center" style="    width: 100%;" data-provides="fileinput">
                                <div class="fileinput-new thumbnail" >
                                     <a href="#" class="btn btn-rose btn-round btn-select"><i class="material-icons">image</i> Select image </a>
                                    <img  id="img-preview" src="{{asset(form.vars.value.media.link)|imagine_filter('actor_thumb')}}"  width="100%">
                                </div>
                               {{form_widget(form.file,{"attr":{"class":"file-hidden input-file img-selector","style":"    display: none;"}})}}
                                <span class="validate-input">{{form_errors(form.file)}}</span>
                           </div>
                       </div>
                       <div class="col-md-8">
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">Full Name</label>
                            {{form_widget(form.name,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.name)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">Type</label>
                            {{form_widget(form.type,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.type)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">Born</label>
                            {{form_widget(form.born,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.born)}}</span>
                        </div>
                       <div class="form-group label-floating is-empty">
                            <label class="control-label">Height</label>
                            {{form_widget(form.height,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.height)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">Biography</label>
                            {{form_widget(form.bio,{"attr":{"class":"form-control","rows":8}})}}
                            <span class="validate-input">{{form_errors(form.bio)}}</span>
                        </div>

                        <br>
                        <span class="pull-right"><a href="{{path("app_actor_index")}}" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
                                                </div>

                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock%}