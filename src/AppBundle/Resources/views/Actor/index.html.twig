{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-4">
						<a href="{{path("app_actor_index")}}" class="btn  btn-lg btn-warning col-md-12"><i class="material-icons" style="font-size: 30px;">refresh</i> Refresh</a>
					</div>
					<div class="col-md-4">
						<a class="btn btn btn-lg btn-yellow col-md-12"><i class="material-icons" style="font-size: 30px;">recent_actors</i> {{actors_count}} Actors</a>
					</div>
					<div class="col-md-4">
						<a href="{{path("app_actor_add")}}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title=""><i class="material-icons" style="font-size: 30px;">add_box</i> NEW ACTOR </a>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<form>
							<input name="q" value="{{app.request.query.get("q")}}" type="text" class="search-input">
							<button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i></button>
						</form>
					</div>
				</div>
				<br>
				<br>
				<div class="row">
					{% for actor in actors %}
						<div class="col-md-3">
							<div class="card card-profile">
								<div class="card-avatar">
									<a href="#">
										<img class="img" src="{{asset(actor.media.link)|imagine_filter('actor_thumb')}}">
									</a>
								</div>
								<div class="card-content">
									<h5 class="card-title"><b>{{actor.name}}</b></h5>
									<a href="{{path("app_actor_edit",{"id":actor.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">edit</i></a>
									<a href="{{path("app_actor_delete",{"id":actor.id})}}" class="btn btn-rose btn-xs"><i class="material-icons">delete</i></a>
								</div>
							</div>
						</div>
					{% else %}
						<div class="col-md-12">
							<div class="card">
								<div class="card-content">
									<br>
									<br>
									<center><img src="{{asset("img/bg_empty.png")}}"  style="width: auto !important;" =""></center>
									<br>
									<br>
								</div>
							</div>
						</div>
					{% endfor %}
				</div>
				<div class=" pull-right">
					{{ knp_pagination_render(actors) }}
				</div>
			</div>
		</div>
		
	{% endblock%}