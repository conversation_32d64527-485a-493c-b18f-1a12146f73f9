{% extends "AppBundle::layout.html.twig" %}
{% block body %}
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ path('app_channel_index') }}" class="btn btn-lg btn-warning col-md-12">
                            <i class="material-icons" style="font-size: 30px;">refresh</i> Refresh
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a class="btn btn-lg btn-yellow col-md-12">
                            <i class="material-icons" style="font-size: 30px;">live_tv</i> {{ channels_count }} TV channels
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ path('app_channel_add') }}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title="">
                            <i class="material-icons" style="font-size: 30px;">add_box</i> NEW TV CHANNEL
                        </a>
                    </div>
                </div>
                <form method="post" action="{{ path('app_channel_update_unique_positions') }}">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Channel</th>
                                        <th>Position</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for channel in channels %}
                                        <tr>
                                            <td>{{ channel.position }}</td>
                                            <td>{{ channel.title }}</td>
                                            <td>
                                                <input type="number" name="positions[{{ channel.id }}]" value="{{ channel.position }}" class="form-control" />
                                            </td>
                                            <td>
                                                <a href="{{ path('app_channel_edit', { 'id': channel.id }) }}" class="btn btn-primary btn-xs">
                                                    <i class="material-icons">edit</i>
                                                </a>
                                                <a href="{{ path('app_channel_delete', { 'id': channel.id }) }}" class="btn btn-danger btn-xs">
                                                    <i class="material-icons">delete</i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            <button type="submit" class="btn btn-success">Save Changes</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}
