{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<div class="container-fluid">
    <div class="row">
      <div class="col-md-3">
        <div class="card">
          <div class="tab-moivie">
            <a href="{{path("app_channel_edit",{"id":channel.id})}}" class="btn btn-tab-movie-active col-md-12"><i class="material-icons">edit</i> Edit</a>
            <a href="{{path("app_channel_sources",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">folder</i> Sources</a>
            <a href="{{path("app_channel_comments",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">comment</i> Comments</a>
            <a href="{{path("app_channel_ratings",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">star_half</i> Ratings</a>
            <a href="#" class="btn btn-tab-movie col-md-12"><i class="material-icons">remove_red_eye</i> {{channel.viewsnumber}} views</a>
            <a href="#" class="btn btn-tab-movie col-md-12"><i class="material-icons">share</i> {{channel.sharesnumber}} shares</a>
            <a href="{{path("app_channel_index")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">arrow_back</i> Cancel</a>

          </div>
        </div>
      </div>
       <div class="col-md-9">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">live_tv</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Edit :  {{form.vars.value.title}}</h4>
                    {{form_start(form)}}
                      <br>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">TV Channel name</label>
                            {{form_widget(form.title,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.title)}}</span>
                        </div>
                        <div class="fileinput fileinput-new text-center" style="    width: 100%;" data-provides="fileinput">
                            <div class="fileinput-new thumbnail" >
                                 <a href="#" class="btn btn-rose btn-round btn-select"><i class="material-icons">image</i> Select image </a>
                                <img  id="img-preview" src="{{asset(form.vars.value.media.link)|imagine_filter('channel_thumb')}}"  width="100%">
                            </div>
                            {{form_widget(form.file,{"attr":{"class":"file-hidden input-file img-selector","style":"    display: none;"}})}}
                            <span class="validate-input">{{form_errors(form.file)}}</span>
                       </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">TV Channel description</label>
                            {{form_widget(form.description,{"attr":{"class":"form-control","rows":8}})}}
                            <span class="validate-input">{{form_errors(form.description)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">TV Channel website</label>
                            {{form_widget(form.website,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.website)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                            <label class="control-label">TV Channel classification</label>
                            {{form_widget(form.classification,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.classification)}}</span>
                        </div>
                      <div class="form-group label-floating is-empty">
                        <label class="control-label">Play TV Channel  :  </label>
                        {{form_widget(form.playas,{"attr":{"class":"form-control"}})}}
                        <span class="validate-input">{{form_errors(form.playas)}}</span>
                      </div>
                        <div class="form-group label-floating ">
                          <label class="control-label">Channel tags (Ex:anim,art,hero)</label>
                          <br>
                          {{form_widget(form.tags,{"attr":{"class":"input-tags"}})}}
                          <span class="validate-input">{{form_errors(form.tags)}}</span>
                        </div>
                        <script>
                          $('.input-tags').selectize({
                          persist: false,
                          createOnBlur: true,
                          create: true
                          });
                        </script>
                        <div class="">
                            <label>
                              {{form_widget(form.enabled)}}  Enabled
                            </label>
                        </div>
                        <div class="">
                            <label>
                              {{form_widget(form.featured)}}  Featured TV Channel
                            </label>
                        </div>
                        <div class="">
                            <label>
                              {{form_widget(form.comment)}}  Enabled Comment
                            </label>
                        </div>
                        <br>
                        {{form_label(form.categories,null,{label_attr:{"style":"font-size:16px"}})}} :
                        <div>
                          <div class="row">
                         {% for field in form.categories %}
                              <label  style="background: #000;border-radius: 2px;padding: 5px;text-align: center;margin: 10px;color: white;font-weight: bold;padding-left: 20px;padding-right: 20px;">
                                  {{ form_widget(field) }} {{ field.vars.label }}
                              </label>
                          {% endfor %}
                          </div>
                        </div>
                        <br>
                        {{form_label(form.countries,null,{label_attr:{"style":"font-size:16px"}})}} :
                        <div>
                          <div class="row">
                         {% for field in form.countries %}
                              <label  style="background: #000;border-radius: 2px;padding: 5px;text-align: center;margin: 10px;color: white;font-weight: bold;padding-left: 20px;padding-right: 20px;">
                                  {{ form_widget(field) }} {{ field.vars.label }}
                              </label>
                          {% endfor %}
                          </div>
                        </div>
                        <br>
                        <span class="pull-right"><a href="{{path("app_channel_index")}}" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock%}