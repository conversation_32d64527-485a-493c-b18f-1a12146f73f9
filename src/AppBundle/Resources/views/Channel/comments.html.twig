{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-3">
				<div class="card">
					<div class="tab-moivie">
						<a href="{{path("app_channel_edit",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">edit</i> Edit</a>
						<a href="{{path("app_channel_sources",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">folder</i> Sources</a>
						<a href="{{path("app_channel_comments",{"id":channel.id})}}" class="btn btn-tab-movie-active  col-md-12"><i class="material-icons">comment</i> Comments</a>
						<a href="{{path("app_channel_ratings",{"id":channel.id})}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">star_half</i> Ratings</a>
						<a href="#" class="btn btn-tab-movie col-md-12"><i class="material-icons">remove_red_eye</i> {{channel.viewsnumber}} views</a>
						<a href="#" class="btn btn-tab-movie col-md-12"><i class="material-icons">share</i> {{channel.sharesnumber}} shares</a>
						<a href="{{path("app_channel_index")}}" class="btn btn-tab-movie col-md-12"><i class="material-icons">arrow_back</i> Cancel</a>
					</div>
				</div>
			</div>
			<div class="col-md-9">
				<div class="card">
					<div class="card-header card-header-icon" data-background-color="rose">
						<i class="material-icons">comment</i>
					</div>
					<div class="card-content">
						<h4 class="card-title">{{channel.title}}: {{count}} Comments </h4>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						{% for comment in pagination %}
							<div class="col-md-12">
								<ul class="timeline timeline-simple">
									<li class="timeline-inverted">
										<div class="timeline-badge info">
											{% if comment.user.media != null %}
												{% if comment.user.media.type =="link" %}
													<img class="avatar-char palette-Red-400 bg"  style="border-radius: 100px;    border: 0.5px solid #ccc;" src="{{comment.user.media.url}}" alt="">
												{% else %}
													<img class="avatar-char palette-Red-400 bg"  style="border-radius: 100px;    border: 0.5px solid #ccc;" src="{{asset(comment.user.media.link)|imagine_filter('actor_thumb')}}" alt="">
												{% endif %}
											{% else %}
												<img class="avatar-char palette-Red-400 bg"  style="border-radius: 100px;    border: 0.5px solid #ccc;" src="{{asset("img/avatar.jpg")|imagine_filter('actor_thumb')}}" alt="">
											{% endif %}
										</div>
										<div class="timeline-panel">
											<div class="timeline-heading">
												<a href="{{path("user_user_edit",{id:comment.user.id})}}" title="">
													<span class="label label-danger">{{comment.user.name}}</span>
												</a>
												<span class="pull-right" >
													<a href="{{path("app_comment_delete",{id:comment.id,"channel":"true"})}}"  rel="tooltip" data-placement="bottom" title="" data-original-title="Delete">
														<i class="material-icons" style="color:red">delete</i>
													</a>
													{% if comment.enabled %}
														<a href="{{path("app_comment_hide",{id:comment.id})}}"  rel="tooltip" data-placement="bottom" title="" data-original-title="Hide">
															<i class="material-icons">visibility_off</i>
														</a>
													{% else %}
														<a href="{{path("app_comment_hide",{id:comment.id})}}"  rel="tooltip" data-placement="bottom" title="" data-original-title="Show">
															<i class="material-icons">remove_red_eye</i>
														</a>
													{% endif %}
												</span>
											</div>
											<div class="timeline-body">
												<p>{{comment.contentclear}}</p>
											</div>
											<hr class="hr-10">
											<small class="pull-right label label-rose">
											<span>{{comment.created|ago}}</span>
											</small>
										</div>
									</li>
									
								</ul>
							</div>
						{% else %}
							<div class="card"  style="text-align: center;" >
								<br>
								<br>
								<img src="{{asset("img/bg_empty.png")}}"  style="width: 100% !important;" ="">
								<br>
								<br>
							</div>
						{% endfor %}
					</div>
					<div class=" pull-right">
						{{ knp_pagination_render(pagination) }}
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock%}