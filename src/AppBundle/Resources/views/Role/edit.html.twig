{% extends "AppBundle::layout.html.twig" %}
{% block body%}
<div class="container-fluid">
    <div class="row">
       <div class="col-sm-offset-2 col-md-8">
            <div class="card">
                <div class="card-header card-header-icon" data-background-color="rose">
                    <i class="material-icons">supervised_user_circle</i>
                </div>
                <div class="card-content">
                    <h4 class="card-title">Edit :  {{form.vars.value.role}}</h4>
                    <br>
                    {{form_start(form)}}
                    <form method="#" action="#">
                        <div class="form-group label-floating">
                            <label class="control-label">Genre role</label>
                            {{form_widget(form.role,{"attr":{"class":"form-control"}})}}
                            <span class="validate-input">{{form_errors(form.role)}}</span>
                        </div>
                        <div class="form-group label-floating is-empty">
                        {{form_widget(form.actor,{"attr":{"class":"form-control form-control-role","placeholder":"Actor/Director"}})}}
                        <span class="validate-input">{{form_errors(form.actor)}}</span>
                        </div>
                        <script type="text/javascript">
                              $("#Role_actor").selectize();
                        </script>
                        <span class="pull-right"><a href="{{path(rout,{"id":poster.id})}}" class="btn btn-fill btn-yellow"><i class="material-icons">arrow_back</i> Cancel</a>{{form_widget(form.save,{attr:{"class":"btn btn-fill btn-rose"}})}}</span>
                    {{form_end(form)}}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock%}