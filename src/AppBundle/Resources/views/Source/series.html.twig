{% extends "AppBundle::layout.html.twig" %}
{% block body %}
    <link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet"/>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>

    <link href="{{ asset('css/jquery.multiselect.css') }}" rel="stylesheet"/>

    <style>
        .main-panel > .content {
            margin-top: 35px;
        }

        .modal-backdrop {
            z-index: -999999;
        }


        .panel-group .panel + .panel {
            margin-top: 15px;
        }


        .checkbox label, .radio label, label, .label-on-left, .label-on-right {
            color: #3C4858;
        }

        .panel .panel-heading a:hover, .panel .panel-heading a:active, .panel .panel-heading a[aria-expanded="true"] {
            color: #000;
        }

        .panel .panel-heading {
            padding: 27px 10px 5px 0px;
        }

        .series_name {
            display: inline-block;
            max-width: 30ch;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .ms-options-wrap {
            display: inline-block !important;
            vertical-align: middle;
            min-width: 200px !important;
            max-width: 200px !important;
            margin-right: 8px;
        }

        .ms-wrapper {
            display: inline-block;
        }

        .ms-options {
            max-height: 400px !important; /* Change this to your desired height */
            overflow-y: auto;
        }

        .ms-options-wrap > button {
            max-width: 100%;
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 38px; /* consistent with Bootstrap */
        }
    </style>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ path("app_source_index") }}" class="btn  btn-lg btn-warning col-md-12"><i
                                    class="material-icons" style="font-size: 30px;">movie</i> Movies List</a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ path("app_source_series") }}" class="btn  btn-lg btn-warning col-md-12"><i
                                    class="material-icons" style="font-size: 30px;">tv</i> Series List</a>
                    </div>
                    <div class="col-md-4">
                        <h2 style="padding-top:0px;margin-top:0px;text-align:right;font-weight: bold;">Series</h2>
                        {# <a href="{{path("app_serie_add")}}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title=""><i class="material-icons" style="font-size: 30px;">add_box</i> NEW SERIE TV </a> #}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <form>
                            <input name="q" value="{{ app.request.query.get("q") }}" type="text" class="search-input">
                            <button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i>
                            </button>
                        </form>
                    </div>
                </div>
                {# <div class="row">
					<div class="col-md-3">
					</div>
					<div class="col-md-9">
					    {% for genre in genres %}
					        <label class="checkbox-inline">
                              <input type="checkbox" name="genreIds[]" value="{{genre.id}}"> {{genre.title}}
                            </label>
					    {% endfor %}
					</div>
				</div> #}
                {# <div class="row">
					<div class="col-md-12">
						<form>
							<input name="q" value="{{app.request.query.get("q")}}" type="text" class="search-input">
							<button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i></button>
						</form>
					</div>
				</div> #}
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-primary" id="btn_save">
                            Save
                        </button>

                        <div style="float:right;margin-top: 20px;">
                            <label>Per page</label>
                            <select class="page-limit" name="page-limit" style="width: 50px">
                                <option value="50" {% if pageLimit == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if pageLimit == 100 %}selected{% endif %}>100</option>
                                <option value="200" {% if pageLimit == 200 %}selected{% endif %}>200</option>
                                <option value="500" {% if pageLimit == 500 %}selected{% endif %}>500</option>
                            </select>
                        </div>
                        <span id="save_status"></span>
                    </div>
                    <div class="col-md-12">
                        {% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
                            <button class="btn btn-info filter-btn" data-type="all" id="show-all">
                                Show all (<span id="series-cnt">{{ series_count }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="missing" id="show-missing">
                                Missing season (<span id="missing-series-cnt">{{ missingCount }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="404" id="show-404">
                                404 (<span id="not-found-series-cnt">{{ notFoundCount }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="enabled" id="show-enabled">
                                Enabled (<span id="enabled-series-cnt">{{ enabledCount }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="notenabled" id="show-notenabled">
                                Not enabled (<span id="not-enabled-series-cnt">{{ notEnabledCount }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="free" id="show-free">
                                Free (<span id="free-series-cnt">{{ freeCount }}</span>)
                            </button>

                            <button class="btn btn-danger filter-btn" data-type="premium" id="show-premium">
                                Premium (<span id="premium-series-cnt">{{ premiumCount }}</span>)
                            </button>
                            <div style="min-width: 200px;" class="ms-wrapper">
                                <select name="basic[]" multiple="multiple" id="genresSelect">
                                    {% for key, items in genres %}
                                        <option value="{{ items.id }}" {{ items.selected }}>{{ items.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div style="min-width: 200px;" class="ms-wrapper">
                                <select name="seriesStatus[]" multiple id="seriesStatusSelect">
                                    {% for items in seriesStatus %}
                                        <option value="{{ items.title }}" {{ items.selected }}>{{ items.title }}</option>
                                    {% endfor %}
                                </select>
                            </div>


                        {% else %}
                            <button class="btn btn-danger filter-btn" data-type="missing" id="show-missing">
                                Missing season ({{ missingCount }})
                            </button>
                            <button class="btn btn-danger filter-btn" data-type="404" id="show-404">
                                404 ({{ notFoundCount }})
                            </button>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-12">
                    <div id="ajax-message"></div>
                </div>
                <div class="col-md-12">
                    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                        Loading...
                    </div>
                </div>
            </div>
            {# <div class=" pull-right">
					{{ knp_pagination_render(series) }}
				</div> #}
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Edit Basic Details of Movie</h4>
                </div>
                <form id="editForm">
                    <div class="modal-body">
                        <input type="hidden" name="source_id" id="source_id"/>
                        <input type="hidden" name="poster_id" id="poster_id"/>
                        <label>Title:</label>
                        <label style="font-weight:bold;font-color:black;">Title of movies</label><br>
                        <label>Source:</label>
                        <input type="text" id="source" name="source" class="form-control" required/>
                        <label>Source Type:</label>
                        <select id="source_type" name="source_type" class="form-control" required>
                            <option value="youtube">youtube</option>
                            <option value="m2u8">m2u8</option>
                            <option value="mov">mov</option>
                            <option value="mp4">mp4</option>
                            <option value="mkv">mkv</option>
                            <option value="webm">webm</option>
                            <option value="embed">embed</option>
                            <option value="file">file</option>
                        </select>
                        <label>Subscription Type:</label>
                        <select id="playas" name="playas" class="form-control" required>
                            <option value="1">Free</option>
                            <option value="2">Premium</option>
                        </select>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <script>
        const protocol = window.location.protocol; // 'http:' or 'https:'
        const host = window.location.host; // e.g., 'supadmin.stagenachos.online'
        const $base_url = `${protocol}//${host}/`;
        $(document).ready(function () {

            $('#genresSelect').multiselect({
                placeholder: 'Select genres',
                search: false,
                selectAll: false,
                onOptionClick: function (element, option) {
                    Cookies.set('genre-ids', $('#genresSelect').val());
                    var fType = 'all';
                    $('.filter-btn').each(function () {
                        if ($(this).hasClass("btn-info")) {
                            fType = $(this).attr('data-type');
                        }
                    });
                    tableRefresh(null, fType);
                    allCntRefresh();
                }
            });
            $('#seriesStatusSelect').multiselect({
                placeholder: 'Select Status',
                search: false,
                selectAll: false,
                onOptionClick: function (element, option) {
                    Cookies.set('series-status', $('#seriesStatusSelect').val());
                    var fType = 'all';
                    $('.filter-btn').each(function () {
                        if ($(this).hasClass("btn-info")) {
                            fType = $(this).attr('data-type');
                        }
                    });
                    tableRefresh(null, fType);
                    allCntRefresh();
                }
            });


            $('#accordion').on('click', '.edit', function () {
                $('#myModal').modal('show');
                var poster_id = $(this).closest('.row').find('.poster_id').val();
            });

            $('#editForm').submit(function (e) {
                e.preventDefault();
                alert('under development');
            });
            $('input[name="genreIds[]"]').on('change', function () {
                tableRefresh();
            });


            var changedRows = Array();
            $('body').on('change', '.txt_title, .txt_season, .txt_episode, .txt_source, .txt_source_type, .ddl_type', function () {
                var id = $(this).parents('.row-id').attr('data-episode-id');
                changedRows[id] = id;

            });
            var changedSerialPart = Array();
            $('body').on('change', '.txt-season-ftp,.txt-season-idmid,.txt-season-year', function () {
                var id = $(this).attr('data-serie-id');
                changedSerialPart.push(id);
            });
            var changedSeries = Array();
            $('body').on('change', '.check_enabled_serie, .ddl_type_serie', function () {
                var id = $(this).attr('data-serie-id');
                changedSeries.push(id);
            });
            $('#btn_save').click(function (e) {
                e.preventDefault();
                $(this).attr('disabled');
                $(this).addClass('disabled');
                $('#save_status').empty().append('Please Wait !!! Saving data is in progress...');
                var count_plus = 0;
                changedRows = changedRows.filter((v, i, a) => a.indexOf(v) === i);
                var count = changedRows.length;
                var countSeries = changedSeries.length;
                var countSerialPart = changedSerialPart.length;
                console.log(countSeries);
                changedRows.forEach(function (x) {
                    var row = $('.row-id' + x);
                    var $id = $(row).attr('id');

                    //$(this).css("background", "green");
                    //$(this).append('<td>'+$id+'</td>');
                    var url = $(row).find('.txt_source').val();
                    var userType = $(row).find('.txt_source').attr('data-user-type');
                    var isMp4 = true;
                    if (userType == 'user' && url) {
                        isMp4 = url.endsWith('.mp4');
                    }
                    if (isMp4) {
                        var data = new FormData();
                        data.append('id', $id);
                        data.append('title', $(row).find('.txt_title').val());
                        data.append('season', $(row).find('.txt_season').val());
                        data.append('episode', $(row).find('.txt_episode').val());
                        data.append('source', $(row).find('.txt_source').val());
                        data.append('source_type', $(row).find('.txt_source_type').val());
                        data.append('playas', $(row).find('.ddl_type').val());
                        data.append('source_id', $(row).find('.source_id').val());
                        data.append('season_id', $(row).find('.season_id').val());
                        data.append('episode_id', $(row).find('.episode_id').val());


                        $.ajax({
                            type: "POST",
                            data: data,
                            url: $base_url + "api/series/update/",
                            mimeType: "multipart/form-data",
                            contentType: false,
                            cache: false,
                            processData: false,
                            success: function (data) {
                                var $data = jQuery.parseJSON(data);

                                /*
                                $('#complaint_id').empty().append($data.complaint);
                                $('#statement_id').empty().append($data.driver_statement);
                                $('#sign_prev').attr( 'src' ,$data.signature);
                                $('#sign_prev').css("display", "block");;

                                $('#hidden_complaint_id').val($data.id);
                                // alertify.success('submitted');
                                // refresh_table(0);
                                // $('#newIncidentModal').modal('hide');
                                */
                                count_plus++;
                                if (countSeries == 0 && count == count_plus) {
                                    afterEach();
                                }
                                console.log(count + ' : ' + count_plus);
                                return;
                            },
                            error: function (data) {
                                //alertify.error('Something Happened');

                            }
                        });
                    } else {
                        alert($(row).find('.txt_season').val() + ' >> ' + $(row).find('.txt_episode').val() + " have not mp4 url.. please add mp4 url");
                    }


                });
                var count_plus = 0;
                changedSeries.forEach(function (x) {
                    var row = $('.row-serie-id' + x);

                    var data = new FormData();
                    data.append('id', x);
                    data.append('enabled', $(row).find('.check_enabled_serie').prop('checked') ? 1 : 0);
                    data.append('ddl_type', $(row).find('.ddl_type_serie').val());


                    $.ajax({
                        type: "POST",
                        data: data,
                        url: $base_url + "api/series-only/update/",
                        mimeType: "multipart/form-data",
                        contentType: false,
                        cache: false,
                        processData: false,
                        success: function (data) {
                            var $data = jQuery.parseJSON(data);

                            /*
                            $('#complaint_id').empty().append($data.complaint);
                            $('#statement_id').empty().append($data.driver_statement);
                            $('#sign_prev').attr( 'src' ,$data.signature);
                            $('#sign_prev').css("display", "block");;

                            $('#hidden_complaint_id').val($data.id);
                            // alertify.success('submitted');
                            // refresh_table(0);
                            // $('#newIncidentModal').modal('hide');
                            */
                            count_plus++;
                            if (countSerialPart == 0 && countSeries == count_plus) {
                                afterEach();
                            }
                            console.log(countSeries + ' : ' + count_plus);
                            return;
                        },
                        error: function (data) {
                            //alertify.error('Something Happened');

                        }
                    });

                });
                var count_plus_serial = 0;
                changedSerialPart.forEach(function (x) {
                    var data = new FormData();
                    data.append('id', x);
                    data.append('ftp', $('#season-ftp-' + x).val());
                    data.append('idmid', $('#season-idmid-' + x).val());
                    data.append('year', $('#season-year-' + x).val());
                    $.ajax({
                        type: "POST",
                        data: data,
                        url: $base_url + "api/series-ftp-year-imdb-id/update/",
                        mimeType: "multipart/form-data",
                        contentType: false,
                        cache: false,
                        processData: false,
                        success: function (data) {
                            count_plus_serial++;
                            if (countSerialPart == count_plus_serial) {
                                afterEach();
                            }
                            console.log(countSeries + ' : ' + count_plus_serial);
                            return;
                        },
                        error: function (data) {
                            //alertify.error('Something Happened');

                        }
                    });

                });


            });
            tableRefresh();
        });

        function afterEach() {
            $('#btn_save').removeAttr('disabled');
            $('#btn_save').removeClass('disabled');
            $('#save_status').empty().append('Updated !!!');
            $("#ajax-message").html(
                '<div class="alert alert-success">Updated successfully.</div>'
            );
            setTimeout(function() {
                location.reload();
            }, 1000);
        }

        function tableRefresh(url, type) {
            var search = $('.search-input').val();

            var limit = $('.page-limit').val();
            $q = "?q=" + search + "&limit=" + limit + "&type=" + type;
            $.ajax({
                type: "POST",
                {# data: data, #}
                url: url ? url : ($base_url + "api/series/list/" + $q),
                mimeType: "multipart/form-data",
                contentType: false,
                cache: false,
                processData: false,
                success: function (data) {
                    {# var $data = jQuery.parseJSON(data); #}
                    $('#accordion').empty().append(data);
                    return;
                },
                error: function (data) {
                    //alertify.error('Something Happened');

                }
            });
        }

        $('body').on('click', '.pagination a', function () {
            tableRefresh($(this).attr('href'));
            return false;
        });
        $('body').on('change', '.page-limit', function () {
            Cookies.set('serie-limit', $(this).val());
            tableRefresh();
            return false;
        });

        $('body').on('click', '.filter-btn', function () {
            refreshAllBtn();
            $(this).addClass('btn-info');
            $(this).removeClass('btn-danger');
            tableRefresh(null, $(this).attr('data-type'));
        });

        function refreshAllBtn() {
            $('.filter-btn').each(function () {
                $(this).removeClass('btn-info');
                $(this).removeClass('btn-danger');
                $(this).addClass('btn-danger');
            });
        }

        function allCntRefresh() {
            $.ajax({
                type: "POST",
                url: $base_url + "api/series/count-all-type",
                mimeType: "multipart/form-data",
                contentType: false,
                cache: false,
                processData: false,
                success: function (data) {
                    var $data = jQuery.parseJSON(data);
                    $("#missing-series-cnt").text($data.data.missingCount);
                    $("#series-cnt").text($data.data.series_count);
                    $("#not-found-series-cnt").text($data.data.notFoundCount);
                    $("#enabled-series-cnt").text($data.data.enabledCount);
                    $("#not-enabled-series-cnt").text($data.data.notEnabledCount);
                    $("#free-series-cnt").text($data.data.freeCount);
                    $("#premium-series-cnt").text($data.data.premiumCount);
                },
                error: function (data) {
                    //alertify.error('Something Happened');

                }
            });
        }
    </script>
    </div>
{% endblock %}