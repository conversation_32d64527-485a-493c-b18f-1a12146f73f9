{% for key, items in series %}
    <div class="panel panel-default">
        <div class="panel-heading" role="tab" id="heading{{ key }}">
            <h4 class="panel-title" style="width: 100%;">
                {% set missingClass ='' %}
                {% set missingStyle ='' %}
                {% if currentSelectedTab =='missing' %}
                    {% set missingClass ='isMissing' %}
                    {% set missingStyle = 'style=background-color:#fa9696;color:#fff;' %}
                {% else %}
                    {% if not items.allSeasonAdded %}
                        {% set missingClass ='isMissing' %}
                        {% set missingStyle = 'style=background-color:#fa9696;color:#fff;' %}
                    {% endif %}
                {% endif %}
                {% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
                <div {{ missingStyle }}
                        class="row allSeasion {{ missingClass }} {% if items.linkInvalid %}isInvalid{% endif %}"
                        {% if items.linkInvalid %}style="background-color: yellow;color: #000000;"{% endif %}>
                    {% else %}
                    <div class="row allSeasion  {{ missingClass }} {% if items.linkInvalid %}isInvalid{% endif %}">
                        {% endif %}
                        {# <div class="row" {% if not items.allSeasonAdded %}style="background-color: #b52a2a;color: #fff;"{%endif%}> #}
                        <a class="collapsed" role="button" data-toggle="collapse" data-parent="#accordion"
                           href="#collapse{{ key }}" aria-expanded="false" aria-controls="collapse{{ key }}">
                            <div class="col-md-3" style="display: flex; height: 45px">
                                {% if items.posterUrl %}
                            <img src="{{ asset("uploads/" ~ items.extension ~ "/" ~ items.posterUrl)|imagine_filter('sereis_poster') }}">
                                {% endif %}&nbsp;&nbsp;
                                <span class="series_name">
							{{ items.title }}
                                    <br>{{ items.titleEnglish }}
                                </span>
                            </div>
                            <div class="col-md-1">
                                <span style="display: inline-block; margin-top: 12px;">{{ items['seasonNames']|length }} seasons</span>
                            </div>
                            <div class="col-md-1">
                                <span style="display: inline-block; margin-top: 12px;">{{ items['episodes']|length }} episodes</span>
                            </div>
                            <div class="col-md-2">
                                <span style="display: inline-block; margin-top: 12px;">{{ items['seriesStatus'] }} </span>
                            </div>
                            {% if not items.allSeasonAdded %}
                                <div class="col-md-2">
                                    <span style="display: inline-block; margin-top: 12px;">{{ items.idmTotalSeason - items['seasonNames']|length }} Seasons missing</span>
                                </div>
                            {% else %}
                                <div class="col-md-2">
                                    <span style="display: inline-block; margin-top: 12px;">
                                    </span>
                                </div>
                            {% endif %}
                        </a>
                        <div class="row-serie-id{{ key }}">
                            <div class="col-md-1" style="margin-top: 10px;">
                                <label>Enabled</label>
                                {% set checked = '' %}
                                {% if items.senabled %}
                                    {% set checked = 'checked' %}
                                {% endif %}
                                {% if app.user and is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin or (app.user.canEnabled ?? false) %}
                                    <input type="checkbox" data-serie-id="{{ key }}"
                                           class="check_enabled_serie" {{ checked }} />
                                {% else %}
                                    <input type="checkbox" data-serie-id="{{ key }}" class="check_enabled_serie"
                                           disabled {{ checked }}/>
                                {% endif %}
                            </div>
                            <div class="col-md-1" style="margin-top: 10px;">
                                <select data-serie-id="{{ key }}" class="ddl_type_serie"
                                        style="color: black; {% if not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin %}display:none;{% endif %}">
                                    {% if items.splayas == 1 %}
                                        <option value="1" selected>Free</option>
                                        <option value="2">Premium</option>
                                        <option value="3">Other</option>
                                    {% elseif items.splayas == 2 %}
                                        <option value="1">Free</option>
                                        <option value="2" selected>Premium</option>
                                        <option value="3">Other</option>
                                    {% else %}
                                        <option value="1">Free</option>
                                        <option value="2">Premium</option>
                                        <option value="3" selected>Other</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                    </div>
            </h4>
        </div>
        <div id="collapse{{ key }}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="heading{{ key }}">
            <div class="panel-body" style="padding-top: 0;">
                <div>
                    <ul class="nav nav-tabs" role="tablist" style="background: #101010;">
                        {% for season_id, season in items['seasonNames']|sort((a, b) => a.position <=> b.position) %}
                            <li role="presentation" {% if loop.first %}class="active"{% endif %} style="width: 100%">
                                <a href="#season{{ season_id }}" aria-controls="#season{{ season_id }}" role="tab"
                                   data-toggle="tab" style="font-size: 20px;float: left;">{{ season.season_title }}</a>
                                {% set isMarkAsCompleted = true %}
                                {% for serie in items['series'][season_id] %}
                                    {% if  serie.source_id is null or serie.url == '' %}
                                        {% set isMarkAsCompleted = false %}
                                    {% endif %}
                                {% endfor %}
                                {% if season.allEpisodeAdded and isMarkAsCompleted %}
                                    <img src="{{ asset("img/check.png") }}" height="20px" width="20px"
                                         style="margin-top: 7px">
                                {% endif %}

                                <a data-season-id="{{ season_id }}" rel="tooltip" data-placement="left"
                                   class=" btn btn-primary btn-xs btn-round add-source" data-original-title="View"
                                   style="float: right;cursor: pointer;">
                                    <i class="material-icons" style="margin-right: -17px;">uploads</i>
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                    <div style="margin-top: 15px; display: flex; gap: 20px; align-items: center; flex-wrap: wrap;">
                        <label for="season-ftp-lbl-{{ key }}">FTP PATH :</label>
                        <input
                                value="{{ items['ftp'] }}"
                                type="text"
                                id="season-ftp-{{ key }}"
                                name="txt-season-ftp-{{ key }}" class="txt-season-ftp"
                                data-serie-id="{{ key }}"
                                style="margin-right: 15px;"
                        >

                        <label for="season-idmid-lbl-{{ key }}">IMDB :</label>
                        <input
                                value="{{ items['idmid'] }}"
                                type="text"
                                id="season-idmid-{{ key }}"
                                name="txt-season-idmid-{{ key }}"
                                class="txt-season-idmid"
                                data-serie-id="{{ key }}"
                                style="margin-right: 15px;"
                        >

                        <label for="season-year-lbl-{{ key }}">YEAR :</label>
                        <input
                                value="{{ items['year'] }}"
                                type="text"
                                id="season-year-{{ key }}"
                                name="txt-season-year-{{ key }}"
                                class="txt-season-year"
                                data-serie-id="{{ key }}"
                        >
                    </div>
                    <div class="tab-content">
                        {% for season_id, season in items['seasonNames'] %}
                            <div role="tabpanel" class="tab-pane {% if loop.first %}active{% endif %}"
                                 id="season{{ season_id }}">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">ID</th>
                                        <th scope="col">Title</th>
                                        <th scope="col">Season</th>
                                        <th scope="col">Episode</th>
                                        <th scope="col">Air Date</th>
                                        <th scope="col">Source</th>
                                        {% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
                                            <th scope="col">Added By</th>
                                        {% endif %}
                                        <th scope="col">Source Type</th>
                                        <th scope="col">Free / Premium</th>
                                    </tr>
                                    </thead>
                                    <tbody id="table-body">
                                    {% for serie in items['series'][season_id] %}
                                        {% if serie and 'episode_id' in serie and serie.episode_id is not null %}
                                            <tr class="row row-id row-id{{ serie.episode_id }}" id="{{ serie.id }}"
                                                data-episode-id="{{ serie.episode_id }}"
                                                {% if serie.linkInvalid %}style="background-color: yellow;"{% endif %}>
                                                {# <tr class="row row-id row-id{{serie.episode_id}}" id="{{serie.id}}" data-episode-id="{{serie.episode_id}}"> #}
                                                <td>
                                                    {{ serie.id }}
                                                    <input type="hidden" class="source_id"
                                                           value="{{ serie.source_id }}"/>
                                                    <input type="hidden" class="season_id"
                                                           value="{{ serie.season_id }}"/>
                                                    <input type="hidden" class="episode_id"
                                                           value="{{ serie.episode_id }}"/>
                                                </td>
                                                <td>
                                                    <input type="text" class="txt_title"
                                                           value="{{ serie.title }}" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin ) and serie.userId != app.user.id %} disabled {% endif %} />
                                                </td>
                                                <td>
                                                    <input type="text" class="txt_season"
                                                           value="{{ serie.season_title }}" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin ) and serie.userId != app.user.id %} disabled {% endif %} />
                                                </td>
                                                <td>
                                                    <input type="text" class="txt_episode"
                                                           value="{{ serie.episode_title }}" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin) and serie.userId != app.user.id %} disabled {% endif %} />
                                                </td>
                                                <td>

                                                    <input type="text" class="txt_episode"
                                                           value="{{ serie.airDate ? serie.airDate|date("d/m/Y") : '' }}" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin) and serie.userId != app.user.id %} disabled {% endif %} />

                                                </td>
                                                <td>
                                                    <input type="text" class="txt_source"
                                                           data-user-type="{% if (not is_granted('ROLE_ADMIN')) %}user{% else %}admin{% endif %}"
                                                           value="{{ serie.url }}" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin ) and serie.userId != app.user.id  and serie.url != '' %} style="display:none" disabled {% endif %} />
                                                </td>
                                                {% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
                                                    <td>
                                                        <label style="color: {{ serie.soucolor ? serie.soucolor : serie.color }}">{{ serie.soufullname ? serie.soufullname : serie.fullname }}</label>
                                                    </td>
                                                {% endif %}
                                                <td>
                                                    <select id="source_type"
                                                            class="txt_source_type" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin) and serie.userId != app.user.id %} disabled {% endif %}>
                                                        <option value="" {% if serie.type == '' %} ok {% endif %}></option>
                                                        <option value="youtube" {% if serie.type == 'youtube' %} selected="selected" {% endif %}>
                                                            Youtube Url
                                                        </option>
                                                        <option value="m3u8" {% if serie.type == 'm3u8' %} selected="selected" {% endif %}>
                                                            m3u8 Url
                                                        </option>
                                                        <option value="mov" {% if serie.type == 'mov' %} selected="selected" {% endif %}>
                                                            MOV Url
                                                        </option>
                                                        <option value="mp4" {% if serie.type == 'mp4' %} selected="selected" {% endif %} {% if serie.type == '' %} selected="selected" {% endif %}>
                                                            MP4 Url
                                                        </option>
                                                        <option value="mkv" {% if serie.type == 'mkv' %} selected="selected" {% endif %}>
                                                            MKV Url
                                                        </option>
                                                        <option value="webm" {% if serie.type == 'webm' %} selected="selected" {% endif %}>
                                                            WEBM Url
                                                        </option>
                                                        <option value="embed" {% if serie.type == 'embed' %} selected="selected" {% endif %}>
                                                            Embed source
                                                        </option>
                                                        <option value="file" {% if serie.type == 'file' %} selected="selected" {% endif %}>
                                                            File (MP4/MOV/MKV/WEBM)
                                                        </option>
                                                    </select>
                                                    {# <input type="text" class="txt_source_type" value="{{serie.type}}" /> #}
                                                </td>
                                                <td>
                                                    <select class="ddl_type" {% if (not is_granted('ROLE_ADMIN') and not app.user.canDoSameAsAdmin) and serie.userId != app.user.id %} disabled {% endif %}>
                                                        {# <option value="1">Free</option>
													<option value="2">Premium</option>
													<option value="3">Other</option> #}
                                                        {% if serie.playas == 1 %}
                                                            <option value="1" selected>Free</option>
                                                            <option value="2">Premium</option>
                                                            <option value="3">Other</option>
                                                        {% elseif serie.playas == 2 %}
                                                            <option value="1">Free</option>
                                                            <option value="2" selected>Premium</option>
                                                            <option value="3">Other</option>
                                                        {% else %}
                                                            <option value="1">Free</option>
                                                            <option value="2">Premium</option>
                                                            <option value="3" selected>Other</option>
                                                        {% endif %}
                                                    </select>
                                                </td>
                                                {# <td>
												<input type="hidden" class="poster_id" value="{{serie.id}}" />
												<a href="#" id="{{serie.source_id}}" class="btn btn-primary edit"><i class="fa fa-pencil" aria-hidden="true"></i></a>
											</td> #}
                                            </tr>
                                        {% endif %}
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="col-md-12">
        <div class="card">
            <div class="card-content">
                <br>
                <br>
                <center><img src="{{ asset("img/bg_empty.png") }}" style="width: auto !important;" =""></center>
                <br>
                <br>
            </div>
        </div>
    </div>
{% endfor %}

<div class=" pull-right">
    {{ knp_pagination_render(paginations, 'AppBundle:Pagination:pagination.html.twig') }}
</div>
<!-- add Modal -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document" style="width:100%">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel">Update Urls</h4>
            </div>
            <form id="addForm">
                <div class="modal-body">
                    {# <input type="hidden" name="edit_paid_user_id" id="edit_paid_user_id" /> #}
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" class="btn btn-default" id="sortUrls" style="float:right">Short urls
                            </button>
                        </div>
                        <div class="col-md-12">
                            <input type="hidden" class="form-control" name="popup_season_id" id="popup_season_id"/>
                            <label>URLs</label>
                            <textarea class="form-control" name="popup_urls" id="popup_urls"
                                      style="height: 400px;"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default close-modal-url" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var scrollPosition;
    $('.add-source').click(function (e) {
        e.preventDefault();
        var seasonId = $(this).attr('data-season-id');
        scrollPosition = $(this).parents('.panel-default').find('.panel-heading').attr('id');
        console.log(scrollPosition);
        $('a[href="#season' + seasonId + '"]').trigger('click');
        $('#addModal').find('#popup_season_id').val(seasonId);
        $('#addModal').modal('show');
    });
    $('#popup_urls').keyup(function (e) {
        var decodedUrl = decodeURIComponent($(this).val());
        $('#popup_urls').val(decodedUrl);
    });
    $('#sortUrls').on('click', function () {
        var urls = $('#popup_urls').val();
        var urlsArray = urls.split('\n');
        const sortedUrls = sortUrlsByEpisodeNumber(urlsArray);
        $('#popup_urls').val(sortedUrls.join('\n'));
    });

    function extractEpisodeNumber(url) {
        const match = url.match(/Episode_(\d+)\.mp4/);
        return match ? parseInt(match[1], 10) : null;
    }

    function sortUrlsByEpisodeNumber(urls) {
        return urls.sort((a, b) => {
            const episodeA = extractEpisodeNumber(a);
            const episodeB = extractEpisodeNumber(b);
            return episodeA - episodeB;
        });
    }

    $('#addForm').submit(function (e) {
        e.preventDefault();
        $('#addModal').modal('hide');
        var seasonId = $('#popup_season_id').val();
        var urls = $('#popup_urls').val();
        var urlsArray = urls.split('\n');
        $('#season' + seasonId).find('.txt_source').each(function (index, element) {
            if (urlsArray[index]) {
                var isDisabled = $(element).attr('disabled');
                if (!isDisabled) {
                    $(element).val(urlsArray[index]);
                    $(element).trigger('change');
                }
            }
        })
        $('#popup_urls').val('');
        $('#popup_urls').html('');
        document.getElementById(scrollPosition).scrollIntoView();
    });
    $('.close-modal-url').on('click', function () {
        $('#popup_urls').val('');
        $('#popup_urls').html('');
    });
    // Restore the scroll position when the modal is hidden
    $('#addModal').on('hidden.bs.modal', function (e) {
        document.getElementById(scrollPosition).scrollIntoView();
    });
</script>