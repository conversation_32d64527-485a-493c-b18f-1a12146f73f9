{% extends "AppBundle::layout.html.twig" %}
{% block body%}
	<script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>
	<style>
	.main-panel > .content {
		    margin-top: 35px;
	}
	.modal-backdrop { z-index: -999999; }
	</style>
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-4">
						<a href="{{path("app_source_index")}}" class="btn  btn-lg btn-warning col-md-12"><i class="material-icons" style="font-size: 30px;">movie</i> Movies List</a>
					</div>
					<div class="col-md-4">
						<a href="{{path("app_source_series")}}" class="btn  btn-lg btn-warning col-md-12"><i class="material-icons" style="font-size: 30px;">tv</i> Series List</a>
					</div>
					<div class="col-md-4">
						<h2 style="padding-top:0px;margin-top:0px;text-align:right;font-weight: bold;">Movies</h2>
						{# <a href="{{path("app_serie_add")}}" class="btn btn-rose btn-lg pull-right add-button col-md-12" title=""><i class="material-icons" style="font-size: 30px;">add_box</i> NEW SERIE TV </a> #}
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<form>
							<input name="q" value="{{app.request.query.get("q")}}" type="text" class="search-input">
							<button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i></button>
						</form>
					</div>
				</div>
				{# <div class="row">
					<div class="col-md-12">
						<form>
							<input name="q" value="{{app.request.query.get("q")}}" type="text" class="search-input">
							<button class="btn btn-sm search-btn"><i class="material-icons" style="font-size: 30px;">search</i></button>
						</form>
					</div>
				</div> #}
				<div class="row">
					<div class="col-md-12">
							<button class="btn btn-primary" id="btn_save">
								Save
							</button>
							<span id="save_status"></span>
							<div style="float:right;margin-top: 20px;">
								<label>Per page</label>
								<select class="page-limit" name="page-limit" style="width: 50px">
									<option value="50" {% if pageLimit == 50 %}selected{% endif %}>50</option>
									<option value="100" {% if pageLimit == 100 %}selected{% endif %}>100</option>
									<option value="200" {% if pageLimit == 200 %}selected{% endif %}>200</option>
									<option value="500" {% if pageLimit == 500 %}selected{% endif %}>500</option>
								</select>
							</div>
					</div>
					<div class="col-md-12">
						{% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
							<button class="btn btn-info filter-btn" data-type="all" id="show-all">
								Show all ({{series_count}})
							</button>
							<button class="btn btn-danger filter-btn" data-type="404" id="show-404">
								404 ({{notFoundCount}})
							</button>
							<button class="btn btn-danger filter-btn" data-type="enabled" id="show-enabled">
								Enabled ({{enabledCount}})
							</button>
							<button class="btn btn-danger filter-btn" data-type="notenabled" id="show-notenabled">
								Not enabled ({{notEnabledCount}})
							</button>
							<button class="btn btn-danger filter-btn" data-type="free" id="show-notenabled">
								Free ({{freeCount}})
							</button>
							<button class="btn btn-danger filter-btn" data-type="premium" id="show-notenabled">
								Premium ({{premiumCount}})
							</button>

						{% else %}
							<button class="btn btn-danger filter-btn" data-type="404" id="show-404">
								404 ({{notFoundCount}})
							</button>
						{% endif %}
					</div>
					<div class="col-md-12">
						<div id="ajax-message"></div>
					</div>
					<div class="col-md-12">
						<table class="table table-striped">
							<thead>
								<tr>
								<th scope="col">#</th>
								<th scope="col">ID</th>
								<th scope="col">Title</th>
								<th scope="col">Source</th>
								{% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
									<th scope="col">Added By</th>
								{% endif %}
								<th scope="col">Source Type</th>
								<th scope="col">Quality</th>
								<th scope="col">Enabled</th>
								<th scope="col">Free / Premium</th>
								{# <th scope="col">Manage</th> #}
								{# <th scope="col">Quality</th> #}
								</tr>
							</thead>
							<tbody id="table-body">
								<tr>
									<td colspan="4">
									Loading...
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				{# <div class=" pull-right">
					{{ knp_pagination_render(series) }}
				</div> #}
			</div>
		</div>
		
		<!-- Modal -->
		<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel">Edit Basic Details of Movie</h4>
			</div>
			<form id="editForm">
			<div class="modal-body">
				<input type="hidden" name="source_id" id="source_id" />
				<input type="hidden" name="poster_id" id="poster_id" />
				<label>Title:</label>
				<label style="font-weight:bold;font-color:black;">Title of movies</label><br>
				<label>Source:</label>
				<input type="text" id="source" name="source" class="form-control" required/>
				<label>Source Type:</label>
				<select id="source_type" name="source_type" class="form-control" required>
					<option value="youtube">youtube</option>
					<option value="m2u8">m2u8</option>
					<option value="mov">mov</option>
					<option value="mp4">mp4</option>
					<option value="mkv">mkv</option>
					<option value="webm">webm</option>
					<option value="embed">embed</option>
					<option value="file">file</option>
				</select>
				<label>Subscription Type:</label>
				<select id="playas" name="playas" class="form-control" required>
					<option value="1">Free</option>
					<option value="2">Premium</option>
				</select>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				<button type="submit" class="btn btn-primary">Save changes</button>
			</div>
			</form>
			</div>
		</div>
		</div>


		<script>
const protocol = window.location.protocol; // 'http:' or 'https:'
const host = window.location.host; // e.g., 'supadmin.stagenachos.online'
const $base_url = `${protocol}//${host}/`;
			$(document).ready(function(){
				

				$('#table-body').on('click','.edit',function(){
					$('#myModal').modal('show');
					var poster_id = $(this).closest('.row').find('.poster_id').val();
				});
				$('#editForm').submit(function(e){
					e.preventDefault();
					alert('under development');
				});
				var changedRows = Array();
				$('body').on('change', '.txt_title, .txt_source, .txt_classification, .txt_source_type, .ddl_type, .check_enabled', function(){
					var id = $(this).parents('.row-id').attr('id');
					changedRows[id] = id;
				});
				$('#btn_save').click(function(e){
					e.preventDefault();
					$(this).attr('disabled');
					$(this).addClass('disabled');
					$('#save_status').empty().append('Please Wait !!! Saving data is in progress...');
					
					var count_plus = 0;
					changedRows = changedRows.filter((v, i, a) => a.indexOf(v) === i);
					var count = changedRows.length;
					changedRows.forEach(function(x){
						var row = $('.row-id#'+x);
						var $id = x;

						var data = new FormData();
						data.append('id',$id);
						data.append('title',$(row).find('.txt_title').val());
						data.append('source',$(row).find('.txt_source').val());
						data.append('classification',$(row).find('.txt_classification').val());
						if ($(row).find(".check_enabled").prop("checked") == true) { 
							data.append('enabled',1);
						}else{
							data.append('enabled',0);
						}
						data.append('source_type',$(row).find('.txt_source_type').val());
						data.append('playas',$(row).find('.ddl_type').val());
						data.append('source_id',$(row).find('.source_id').val());


						 $.ajax({
							type: "POST",
							data: data,
							url: $base_url+"api/movie/update/",
							mimeType: "multipart/form-data",
							contentType: false,
							cache: false,
							processData: false,
							success: function(data) {
								var $data = jQuery.parseJSON(data);
								/*
								$('#complaint_id').empty().append($data.complaint);
								$('#statement_id').empty().append($data.driver_statement);
								$('#sign_prev').attr( 'src' ,$data.signature);
								$('#sign_prev').css("display", "block");;

								$('#hidden_complaint_id').val($data.id);
								// alertify.success('submitted');
								// refresh_table(0);
								// $('#newIncidentModal').modal('hide');
								*/
								count_plus++;
								if(count == count_plus){
									afterEach();
								}
								console.log(count +' : '+count_plus);
								return;
							},
							error: function(data) {
								//alertify.error('Something Happened');

							}
						});
						 
					});

					

				});
				tableRefresh();
				$('.search-btn').on('click', function(){
					tableRefresh();
				});
			});
			function afterEach(){
				$('#btn_save').removeAttr('disabled');
				$('#btn_save').removeClass('disabled');
				$('#save_status').empty().append('Updated !!!');
				$("#ajax-message").html(
						'<div class="alert alert-success">Updated successfully.</div>'
				);
				setTimeout(function() {
					location.reload();
				}, 1000);
			}
			function tableRefresh(url, type){
				var search = $('.search-input').val();
				var limit = $('.page-limit').val();
				$q = "?q="+search+"&limit="+limit+"&type="+type;
				$.ajax({
							type: "POST",
							url: url ? url : ($base_url+"api/movie/list/" + $q),
							mimeType: "multipart/form-data",
							contentType: false,
							cache: false,
							processData: false,
							success: function(data) {
								$('#table-body').empty().append(data);
								return;
							},
							error: function(data) {
								//alertify.error('Something Happened');

							}
						});
			}
			$('body').on('click', '.pagination a', function(){
				tableRefresh($(this).attr('href'));
				return false;
			});

			$('body').on('change', '.page-limit', function(){
				Cookies.set('movie-limit', $(this).val());
				tableRefresh();
				return false;
			});
			$('body').on('click', '.filter-btn', function(){
				refreshAllBtn();
				$(this).addClass('btn-info');
				$(this).removeClass('btn-danger');
				tableRefresh(null, $(this).attr('data-type'));
			});
			function refreshAllBtn(){
				$('.filter-btn').each(function(){
					$(this).removeClass('btn-info');
					$(this).removeClass('btn-danger');
					$(this).addClass('btn-danger');
				});
			}
		</script>
	{% endblock%}