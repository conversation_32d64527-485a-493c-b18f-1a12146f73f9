{% for serie in series %}

		<tr class="row row-id" id="{{serie.id}}" {% if serie.linkInvalid %}style="background-color:yellow"{% endif %}>
		{# <tr class="row row-id" id="{{serie.id}}"> #}
			<td>
				{% if serie.posterUrl %}
					<img src="{{asset("uploads/" ~ serie.extension ~ "/" ~ serie.posterUrl)|imagine_filter('sereis_poster')}}"  >
				{% endif %}&nbsp;&nbsp;{{serie.id}}
			<input type="hidden" class="source_id" value="{{serie.source_id}}" />
			</td>
			<td>
				<input type="text" class="txt_title" value="{{serie.title}}" />
				
			</td>
			<td>
				<input type="text" class="txt_source" value="{{serie.url}}" />
			</td>
			{% if is_granted('ROLE_ADMIN') or app.user.canDoSameAsAdmin %}
				<td>
					<label style="color: {{serie.color}}">{{serie.fullname}}</label>
				</td>
			{% endif %}
			<td>
				<select id="Source_type" class="txt_source_type">
					<option value="" {% if serie.type == '' %} ok {% endif %}></option>
					<option value="youtube" {% if serie.type == 'youtube' %} selected="selected" {% endif %}>Youtube Url</option>
					<option value="m3u8"  {% if serie.type == 'm3u8' %} selected="selected" {% endif %}>m3u8 Url</option>
					<option value="mov" {% if serie.type == 'mov' %} selected="selected" {% endif %}>MOV Url</option>
					<option value="mp4" {% if serie.type == 'mp4' %} selected="selected" {% endif %} {% if serie.type == '' %} selected="selected" {% endif %}>MP4 Url</option>
					<option value="mkv" {% if serie.type == 'mkv' %} selected="selected" {% endif %}>MKV Url</option>
					<option value="webm" {% if serie.type == 'webm' %} selected="selected" {% endif %}>WEBM Url</option>
					<option value="embed" {% if serie.type == 'embed' %} selected="selected" {% endif %}>Embed source</option>
					<option value="file" {% if serie.type == 'file' %} selected="selected" {% endif %}>File (MP4/MOV/MKV/WEBM)</option>
				</select>
				{# <input type="text" class="txt_source_type" value="{{serie.type}}" /> #}
			</td>
			<td>
				<input type="text" class="txt_classification" value="{{serie.classification}}" />
			</td>
			<td>
				<input type="checkbox" class="check_enabled" {% if serie.enabled == '1' %} checked {% endif %} />
				{# <select id="Source_type" class="ddl_enabled">
					<option value="1" {% if serie.type == '1' %} selected="selected" {% endif %}>Enabled</option>
					<option value="0"  {% if serie.type == '0' %} selected="selected" {% endif %}>Disabled</option>
				</select> #}
			</td>
			<td>
				<select  class="ddl_type">
					{# <option value="1">Free</option>
					<option value="2">Premium</option>
					<option value="3">Other</option> #}
					{% if serie.playas == 1  %}
						<option value="1" selected>Free</option>
						<option value="2">Premium</option>
						<option value="3">Other</option>
					{% elseif serie.playas == 2 %}
						<option value="1">Free</option>
						<option value="2" selected>Premium</option>
						<option value="3">Other</option>
					{% else %}
						<option value="1">Free</option>
						<option value="2">Premium</option>
						<option value="3" selected>Other</option>
					{% endif %}
				</select>
			</td>
			{# <td>
				<input type="hidden" class="poster_id" value="{{serie.id}}" />
				<a href="#" id="{{serie.source_id}}" class="btn btn-primary edit"><i class="fa fa-pencil" aria-hidden="true"></i></a>
			</td> #}
		</tr>
			
		{# <div class="card">
			<div class="card-content" style="text-align: center;   padding: 0px;">
				<img src="{{asset(serie.poster.link)|imagine_filter('poster_thumb')}}"  >
				<br>
				<span class="label-lang">{{serie.title}}</span>
			</div>
			<div class="card-footer" style="    text-align: center;">
				<a href="{{path("app_serie_edit",{"id":serie.id})}}" rel="tooltip" data-placement="left" class=" btn btn-primary btn-xs btn-round" data-original-title="Edit">
					<i class="material-icons">edit</i>
				</a>
				{% if serie.cover != null  %}
					<a href="{{path("app_home_notif_poster",{title:serie.title,id:serie.id,image:asset(serie.cover.link)|imagine_filter("cover_thumb"),icon:asset(serie.poster.link)|imagine_filter("poster_thumb")})}}" rel="tooltip" data-placement="left" class=" btn btn-primary btn-xs btn-round" data-original-title="Noitifcation">
						<i class="material-icons">notifications</i>
					</a>
				{% else %}
					<a href="{{path("app_home_notif_poster",{title:serie.title,id:serie.id,image:asset(serie.poster.link)|imagine_filter("cover_thumb"),icon:asset(serie.poster.link)|imagine_filter("poster_thumb")})}}" rel="tooltip" data-placement="left" class=" btn btn-primary btn-xs btn-round" data-original-title="Noitifcation">
						<i class="material-icons">notifications</i>
					</a>						
				{% endif %}
				<a href="{{path("app_serie_delete",{"id":serie.id})}}" rel="tooltip" data-placement="left" class=" btn btn-danger btn-xs btn-round" data-original-title="Delete">
					<i class="material-icons">delete</i>
				</a>
			</div>
		</div> #}
	
{% else %}
	<div class="col-md-12">
		<div class="card">
			<div class="card-content">
				<br>
				<br>
				<center><img src="{{asset("img/bg_empty.png")}}"  style="width: auto !important;" =""></center>
				<br>
				<br>
			</div>
		</div>
	</div>
{% endfor %}
<tr>
	<td colspan="7">
		<div class=" pull-right">
			{{ knp_pagination_render(series, 'AppBundle:Pagination:pagination.html.twig') }}
		</div>
	<td>
</tr>