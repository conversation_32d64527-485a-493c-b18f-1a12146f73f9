{% extends "AppBundle::layout.html.twig" %}
{% block navbar_title %}
    <a class="navbar-brand" href="#">Hours Report</a>
{% endblock %}
{% block body%}
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script src="https://cdn.datatables.net/2.0.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.0.2/js/dataTables.bootstrap4.js"></script>
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">remove_red_eye</i>
                </div>
                <div class="card-content">
                    <p class="category">Today Hours</p>
                    <h3 class="title">{{(today_stats.totalHours / (1000 * 60 * 60))|number_format(2, '.', ',')}}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">remove_red_eye</i>
                </div>
                <div class="card-content">
                    <p class="category">This Week Hours</p>
                    <h3 class="title">{{(weekly_stats.totalHours / (1000 * 60 * 60))|number_format(2, '.', ',')}}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">remove_red_eye</i>
                </div>
                <div class="card-content">
                    <p class="category">This Month Hours</p>
                     <h3 class="title">{{(monthly_stats.totalHours / (1000 * 60 * 60))|number_format(2, '.', ',') }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">remove_red_eye</i>
                </div>
                <div class="card-content">
                    <p class="category">This Year Hours</p>
                     <h3 class="title">{{(yearly_stats.totalHours / (1000 * 60 * 60))|number_format(2, '.', ',') }}</h3>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">group</i>
                </div>
                <div class="card-content">
                    <p class="category">Today Users</p>
                    <h3 class="title">{{(today_stats.numUsers)}}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">group</i>
                </div>
                <div class="card-content">
                    <p class="category">This Week Users</p>
                    <h3 class="title">{{(weekly_stats.numUsers)}}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">group</i>
                </div>
                <div class="card-content">
                    <p class="category">This Month Users</p>
                     <h3 class="title">{{(monthly_stats.numUsers) }}</h3>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-header" data-background-color="black">
                    <i class="material-icons">group</i>
                </div>
                <div class="card-content">
                    <p class="category">This Year Users</p>
                     <h3 class="title">{{(yearly_stats.numUsers) }}</h3>
                </div>
            </div>
        </div>
    </div>
    {% if hoursByUsers|length > 0 %}
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-content">
                        <h4 class="card-title">Users State</h4>
                        <table id="dtBasicExample" class="table table-striped table-bordered table-sm" cellspacing="0" width="100%">
                                <thead class="text-primary">
                                    <tr>
                                        <th class="th-sm">Email</th>
                                        <th class="th-sm">IP</th>
                                        <th class="th-sm">Today</th>
                                        <th class="th-sm">This Week</th>
                                        <th class="th-sm">This Month</th>
                                        <th class="th-sm">This Year</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for email, hoursByUser in hoursByUsers %}
                                        <tr>
                                            <td>
                                                {{email}}
                                            </td>
                                            <td>
                                                {{hoursByUser.ip is defined ? hoursByUser.ip : '' }}
                                            </td>
                                            <td>{{hoursByUser.today is defined ? (hoursByUser.today / (1000 * 60 * 60))|number_format(2, '.', ',') : 0}}</td>
                                            <td>{{hoursByUser.week is defined ? (hoursByUser.week / (1000 * 60 * 60))|number_format(2, '.', ',') : 0}}</td>
                                            <td>{{hoursByUser.month is defined ? (hoursByUser.month / (1000 * 60 * 60))|number_format(2, '.', ',') : 0}}</td>
                                            <td>{{hoursByUser.year is defined ? (hoursByUser.year / (1000 * 60 * 60))|number_format(2, '.', ',') : 0}}</td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3">
                                                <br>
                                                <br>
                                                <center><img src="{{asset("img/bg_empty.png")}}"  style="width: auto !important;"></center>
                                                <br>
                                                <br>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                    </div>
                </div>
            </div>
        </div>
        <script>
            $('#dtBasicExample').DataTable({
                "pagingType": 'full_numbers',
                "lengthChange": false,
                "pageLength": 100
            });
        </script>
    {% else %}
        <div class="row">
            <div class="col-md-12">
                <a href="/reports?full=1">See full report</a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock%}