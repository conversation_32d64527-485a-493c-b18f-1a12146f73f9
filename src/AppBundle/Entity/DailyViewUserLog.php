<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;
use UserBundle\Entity\User;

/**
 * Watch
 *
 * @ORM\Table(name="daily_view_user_log")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\DailyViewCountRepository")
 */
class DailyViewUserLog
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\DailyViewCount")
     * @ORM\JoinColumn(name="view_count_id", referencedColumnName="id", onDelete="CASCADE", nullable=false)
     */
    private $viewCount;


    /**
     * @ORM\ManyToOne(targetEntity="UserBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false)
     */
    private $user;

    /**
     * Get id
     * @return
     */
    public function getId()
    {
        return $this->id;
    }


    public function getViewCount(): ?DailyViewCount
    {
        return $this->viewCount;
    }

    public function setViewCount(?DailyViewCount $viewCount): void
    {
        $this->viewCount = $viewCount;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): void
    {
        $this->user = $user;
    }

}
