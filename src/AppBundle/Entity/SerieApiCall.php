<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * SerieApiCall
 *
 * @ORM\Table(name="serie_api_call")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\SerieApiCallRepository")
 */
class SerieApiCall
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\Column(name="serie_id", type="integer")
     */
    private $serieId;

    /**
     * @var bool
     *
     * @ORM\Column(name="processed", type="boolean")
     */
    private $processed = false;

    /**
     * @var bool
     *
     * @ORM\Column(name="progress", type="boolean")
     */
    private $progress = false;

    /**
     * @var string
     * @ORM\Column(name="error_message", type="text", nullable=true))
     */
    private $message;

    /**
     * @ORM\ManyToOne(targetEntity="UserBundle\Entity\User")
     * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
     */
    private $user;


    /**
     * Get id.
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set serieId.
     *
     * @param int $serieId
     *
     * @return SerieApiCall
     */
    public function setSerieId($serieId)
    {
        $this->serieId = $serieId;

        return $this;
    }

    /**
     * Get serieId.
     *
     * @return int
     */
    public function getSerieId()
    {
        return $this->serieId;
    }

    /**
     * Set processed.
     *
     * @param bool $processed
     *
     * @return SerieApiCall
     */
    public function setProcessed($processed)
    {
        $this->processed = $processed;

        return $this;
    }

    /**
     * Get processed.
     *
     * @return bool
     */
    public function getProcessed()
    {
        return $this->processed;
    }

    /**
     * Set progress.
     *
     * @param bool $progress
     *
     * @return SerieApiCall
     */
    public function setProgress($progress)
    {
        $this->progress = $progress;

        return $this;
    }

    /**
     * Get progress.
     *
     * @return bool
     */
    public function getProgress()
    {
        return $this->progress;
    }

    /**
     * Set message.
     *
     * @param bool $message
     *
     * @return SerieApiCall
     */
    public function setMessage($message)
    {
        $this->message = $message;

        return $this;
    }

    /**
     * Get message.
     *
     * @return bool
     */
    public function getMessage()
    {
        return $this->message;
    }

    /**
     * Set user
     *
     * @param \UserBundle\Entity\User $user
     *
     * @return YourEntity
     */
    public function setUser(\UserBundle\Entity\User $user = null)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return \UserBundle\Entity\User
     */
    public function getUser()
    {
        return $this->user;
    }

    public function addMessage($message){
        $oldMessage = $this->message .'\n' . $message;
        $oldMessage = trim($oldMessage, '\n');
        $this->setMessage($oldMessage);
    }
}
