<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

/**
 * Watch
 *
 * @ORM\Table(name="daily_view_count")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\DailyViewCountRepository")
 */
class DailyViewCount
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @var int
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Poster")
     * @ORM\JoinColumn(name="poster_id", referencedColumnName="id")
     */
    private $posters;


    /**
     * @var int
     *
     * @ORM\Column(name="total_views", type="integer")
     */
    private $totalViews;

    /**
     * @ORM\Column(name="view_date", type="date")
     */
    private $viewDate;

    /**
     * @return mixed
     */
    public function getViewDate()
    {
        return $this->viewDate;
    }

    /**
     * @param mixed $viewDate
     */
    public function setViewDate($viewDate): void
    {
        $this->viewDate = $viewDate;
    }

    public function getTotalViews(): int
    {
        return $this->totalViews;
    }

    public function setTotalViews(int $totalViews): void
    {
        $this->totalViews = $totalViews;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getPosters(): ?Poster
    {
        return $this->posters;
    }

    public function setPosters(?Poster $poster): void
    {
        $this->posters = $poster;
    }

}
