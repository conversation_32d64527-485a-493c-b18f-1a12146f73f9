<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use AppBundle\Entity\Poster;

/**
 * @ORM\Entity
 * @ORM\Table(name="trending_poster")
 */
class TrendingPoster
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * Many TrendingPosters belong to one Poster (owning side).
     *
     * @ORM\ManyToOne(targetEntity="AppBundle\Entity\Poster")
     * @ORM\JoinColumn(name="poster_id", referencedColumnName="id", nullable=false, onDelete="CASCADE")
     */
    private $poster;

    /**
     * @ORM\Column(type="integer", options={"default": 0})
     */
    private $sortOrder = 0;


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPoster(): ?Poster
    {
        return $this->poster;
    }

    public function setPoster(Poster $poster): self
    {
        $this->poster = $poster;
        return $this;
    }

    public function getSortOrder(): ?int
    {
        return $this->sortOrder;
    }

    public function setSortOrder(?int $sortOrder): self
    {
        $this->sortOrder = $sortOrder;
        return $this;
    }
}
