<?php

namespace AppBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * NewContent
 *
 * @ORM\Table(name="new_content")
 * @ORM\Entity(repositoryClass="AppBundle\Repository\NewContentRepository")
 */
class NewContent
{
    /**
     * @var int
     *
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity="Poster")
     * @ORM\JoinColumn(name="poster_id", referencedColumnName="id", nullable=true)
     */
    private $poster;

    /**
     * @var string
     *
     * @ORM\Column(name="expireDate", type="string", length=255)
     */
    private $expireDate;


    /**
     * Get id.
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set poster.
     *
     * @param $poster
     *
     * @return NewContent
     */
    public function setPoster($poster)
    {
        $this->poster = $poster;

        return $this;
    }

    /**
     * Get poster.
     *
     * @return object
     */
    public function getPoster()
    {
        return $this->poster;
    }

    /**
     * Set expireDate.
     *
     * @param string $expireDate
     *
     * @return NewContent
     */
    public function setExpireDate($expireDate)
    {
        $this->expireDate = $expireDate;

        return $this;
    }

    /**
     * Get expireDate.
     *
     * @return string
     */
    public function getExpireDate()
    {
        return $this->expireDate;
    }
}
