<?php

namespace AppBundle\Service;

use AppBundle\Entity\DailyViewCount;
use AppBundle\Entity\DailyViewUserLog;
use Doctrine\ORM\EntityManagerInterface;
/**
 *
 */
class CommonService
{

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @param $em
     */
    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @param $objUser
     * @param $objPoster
     * @return void
     */
    public function manageViewCount($objUser, $objPoster)
    {
        $objDailyViewCount = $this->entityManager
            ->getRepository(DailyViewCount::class)
            ->findOneBy([
                'viewDate' => new \DateTime('today'),
                'posters' => $objPoster
            ]);
        if ($objDailyViewCount instanceof DailyViewCount) {

            $objDailyViewUserLog = $this->entityManager
                ->getRepository(DailyViewUserLog::class)
                ->findOneBy([
                    'viewCount' => $objDailyViewCount,
                    'user' => $objUser
                ]);
            if ($objDailyViewUserLog === null) {
                $objDailyViewCount->setTotalViews($objDailyViewCount->getTotalViews() + 1);
                $this->entityManager->persist($objDailyViewCount);
                $objDailyViewUserLog = new DailyViewUserLog();
                $objDailyViewUserLog->setUser($objUser);
                $objDailyViewUserLog->setViewCount($objDailyViewCount);
                $this->entityManager->persist($objDailyViewUserLog);
                $this->entityManager->flush();
            }
        } else {
            $objDailyViewCount = new DailyViewCount();
            $objDailyViewCount->setPosters($objPoster);
            $objDailyViewCount->setViewDate(new \DateTime('today'));
            $objDailyViewCount->setTotalViews(1);
            $objDailyViewUserLog = new DailyViewUserLog();
            $objDailyViewUserLog->setUser($objUser);
            $objDailyViewUserLog->setViewCount($objDailyViewCount);
            $this->entityManager->persist($objDailyViewCount);
            $this->entityManager->persist($objDailyViewUserLog);
            $this->entityManager->flush();
        }
    }
}
